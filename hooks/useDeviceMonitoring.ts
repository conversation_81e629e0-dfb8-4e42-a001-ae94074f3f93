import { useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import { snmpService, DeviceInfo } from '../services/SNMPService';

export function useDeviceMonitoring() {
  const [devices, setDevices] = useState<DeviceInfo[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const scanDevices = useCallback(async () => {
    if (Platform.OS === 'web') {
      setError('SNMP scanning is not supported in web browser');
      return;
    }

    setIsScanning(true);
    setError(null);

    try {
      // For demo purposes, we'll use a fixed subnet
      const subnet = '192.168.1';
      const discoveredDevices = await snmpService.discoverDevices(subnet);
      setDevices(discoveredDevices);
    } catch (err) {
      setError('Failed to scan for devices. Please check your network connection.');
    } finally {
      setIsScanning(false);
    }
  }, []);

  useEffect(() => {
    // Cleanup when component unmounts
    return () => {
      snmpService.cleanup();
    };
  }, []);

  return {
    devices,
    isScanning,
    error,
    scanDevices,
  };
}