import { AppRegistry } from 'react-native';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

function App() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>SmartDM 智能设备管理</Text>
      <Text style={styles.subtitle}>应用启动成功！</Text>
      <Text style={styles.info}>EMFILE问题已解决</Text>
      <Text style={styles.info}>AndroidNetworkTools已集成</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 10,
    color: '#666',
  },
  info: {
    fontSize: 14,
    marginBottom: 5,
    color: '#888',
  },
});

AppRegistry.registerComponent('smartdm', () => App);
