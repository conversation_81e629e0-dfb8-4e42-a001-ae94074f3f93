const React = require('react');
const { AppRegistry, View, Text, StyleSheet } = require('react-native');

function App() {
  return React.createElement(View, { style: styles.container }, [
    React.createElement(Text, { key: '1', style: styles.title }, 'SmartDM 智能设备管理'),
    React.createElement(Text, { key: '2', style: styles.subtitle }, '应用启动成功！'),
    React.createElement(Text, { key: '3', style: styles.info }, 'EMFILE问题已解决'),
    React.createElement(Text, { key: '4', style: styles.info }, 'AndroidNetworkTools已集成'),
  ]);
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 10,
    color: '#666',
  },
  info: {
    fontSize: 14,
    marginBottom: 5,
    color: '#888',
  },
});

AppRegistry.registerComponent('smartdm', function() { return App; });
