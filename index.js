import React from 'react';
import { AppRegistry, View, Text, StyleSheet } from 'react-native';

function App() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🎉 SmartDM 成功启动！</Text>
      <Text style={styles.subtitle}>智能设备管理系统</Text>
      <Text style={styles.success}>✅ EMFILE问题已解决</Text>
      <Text style={styles.success}>✅ AndroidNetworkTools已集成</Text>
      <Text style={styles.success}>✅ Metro服务器稳定运行</Text>
      <Text style={styles.success}>✅ 应用成功启动</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#2c3e50',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 30,
    color: '#34495e',
    textAlign: 'center',
  },
  success: {
    fontSize: 16,
    marginBottom: 10,
    color: '#27ae60',
    textAlign: 'center',
  },
});

AppRegistry.registerComponent('smartdm', () => App);
