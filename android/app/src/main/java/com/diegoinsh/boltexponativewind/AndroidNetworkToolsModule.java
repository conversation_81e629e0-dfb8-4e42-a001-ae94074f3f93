package com.diegoinsh.boltexponativewind;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.AsyncTask;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class AndroidNetworkToolsModule extends ReactContextBaseJavaModule {
    private static final String MODULE_NAME = "AndroidNetworkTools";
    private final ReactApplicationContext reactContext;

    public AndroidNetworkToolsModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @ReactMethod
    public void isAvailable(Promise promise) {
        try {
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void getWifiInfo(Promise promise) {
        try {
            WritableMap result = Arguments.createMap();

            ConnectivityManager connectivityManager = (ConnectivityManager) reactContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            WifiManager wifiManager = (WifiManager) reactContext.getApplicationContext().getSystemService(Context.WIFI_SERVICE);

            if (connectivityManager != null && wifiManager != null) {
                NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                boolean isConnected = networkInfo != null && networkInfo.isConnected() && networkInfo.getType() == ConnectivityManager.TYPE_WIFI;

                result.putBoolean("isConnected", isConnected);
                result.putString("type", isConnected ? "wifi" : null);

                if (isConnected) {
                    WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                    if (wifiInfo != null) {
                        // 获取SSID
                        String ssid = wifiInfo.getSSID();
                        if (ssid != null && !ssid.equals("<unknown ssid>")) {
                            // 移除引号
                            ssid = ssid.replace("\"", "");
                            result.putString("ssid", ssid);
                        }

                        // 获取IP地址
                        int ipAddress = wifiInfo.getIpAddress();
                        if (ipAddress != 0) {
                            String ip = String.format("%d.%d.%d.%d",
                                (ipAddress & 0xff),
                                (ipAddress >> 8 & 0xff),
                                (ipAddress >> 16 & 0xff),
                                (ipAddress >> 24 & 0xff));
                            result.putString("ipAddress", ip);
                        }
                    }
                } else {
                    result.putString("ssid", null);
                    result.putString("ipAddress", null);
                }
            } else {
                result.putBoolean("isConnected", false);
                result.putString("type", null);
                result.putString("ssid", null);
                result.putString("ipAddress", null);
            }

            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }



    @ReactMethod
    public void getLocalIPAddress(Promise promise) {
        try {
            WifiManager wifiManager = (WifiManager) reactContext.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                int ipAddress = wifiInfo.getIpAddress();
                
                String ip = String.format("%d.%d.%d.%d",
                    (ipAddress & 0xff),
                    (ipAddress >> 8 & 0xff),
                    (ipAddress >> 16 & 0xff),
                    (ipAddress >> 24 & 0xff));
                
                promise.resolve(ip);
            } else {
                promise.resolve(null);
            }
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void ping(String ip, int timeout, Promise promise) {
        new AsyncTask<Void, Void, WritableMap>() {
            @Override
            protected WritableMap doInBackground(Void... voids) {
                WritableMap result = Arguments.createMap();
                result.putString("ip", ip);
                
                try {
                    long startTime = System.currentTimeMillis();
                    InetAddress address = InetAddress.getByName(ip);
                    boolean isReachable = address.isReachable(timeout);
                    long responseTime = System.currentTimeMillis() - startTime;
                    
                    result.putBoolean("isReachable", isReachable);
                    if (isReachable) {
                        result.putDouble("responseTime", responseTime);
                    }
                } catch (Exception e) {
                    result.putBoolean("isReachable", false);
                    result.putString("error", e.getMessage());
                }
                
                return result;
            }
            
            @Override
            protected void onPostExecute(WritableMap result) {
                promise.resolve(result);
            }
        }.execute();
    }

    @ReactMethod
    public void scanSubnet(String subnet, Promise promise) {
        new AsyncTask<Void, Void, WritableArray>() {
            @Override
            protected WritableArray doInBackground(Void... voids) {
                WritableArray devices = Arguments.createArray();
                ExecutorService executor = Executors.newFixedThreadPool(50);
                List<Future<WritableMap>> futures = new ArrayList<>();
                
                try {
                    for (int i = 1; i <= 254; i++) {
                        final String ip = subnet + "." + i;
                        
                        Future<WritableMap> future = executor.submit(() -> {
                            WritableMap device = Arguments.createMap();
                            device.putString("ip", ip);
                            
                            try {
                                InetAddress address = InetAddress.getByName(ip);
                                long startTime = System.currentTimeMillis();
                                boolean isReachable = address.isReachable(1000);
                                long responseTime = System.currentTimeMillis() - startTime;
                                
                                device.putBoolean("isReachable", isReachable);
                                if (isReachable) {
                                    device.putDouble("responseTime", responseTime);
                                    String hostname = address.getHostName();
                                    if (hostname != null && !hostname.equals(ip)) {
                                        device.putString("hostname", hostname);
                                    }
                                }
                            } catch (Exception e) {
                                device.putBoolean("isReachable", false);
                            }
                            
                            return device;
                        });
                        
                        futures.add(future);
                    }
                    
                    // 收集结果
                    for (Future<WritableMap> future : futures) {
                        try {
                            WritableMap device = future.get(2, TimeUnit.SECONDS);
                            if (device.getBoolean("isReachable")) {
                                devices.pushMap(device);
                            }
                        } catch (Exception e) {
                            // 忽略超时的设备
                        }
                    }
                    
                } catch (Exception e) {
                    // 处理异常
                } finally {
                    executor.shutdown();
                }
                
                return devices;
            }
            
            @Override
            protected void onPostExecute(WritableArray devices) {
                promise.resolve(devices);
            }
        }.execute();
    }

    @ReactMethod
    public void scanPort(String ip, int port, int timeout, Promise promise) {
        new AsyncTask<Void, Void, Boolean>() {
            @Override
            protected Boolean doInBackground(Void... voids) {
                try {
                    Socket socket = new Socket();
                    socket.connect(new InetSocketAddress(ip, port), timeout);
                    socket.close();
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }
            
            @Override
            protected void onPostExecute(Boolean result) {
                promise.resolve(result);
            }
        }.execute();
    }

    @ReactMethod
    public void wakeOnLan(String mac, String ip, Promise promise) {
        new AsyncTask<Void, Void, Boolean>() {
            @Override
            protected Boolean doInBackground(Void... voids) {
                try {
                    // 构建Magic Packet
                    byte[] macBytes = new byte[6];
                    String[] hex = mac.split(":");
                    for (int i = 0; i < 6; i++) {
                        macBytes[i] = (byte) Integer.parseInt(hex[i], 16);
                    }
                    
                    byte[] packet = new byte[102];
                    // 前6个字节为0xFF
                    for (int i = 0; i < 6; i++) {
                        packet[i] = (byte) 0xFF;
                    }
                    // 重复16次MAC地址
                    for (int i = 1; i <= 16; i++) {
                        System.arraycopy(macBytes, 0, packet, i * 6, 6);
                    }
                    
                    // 发送UDP包
                    DatagramSocket socket = new DatagramSocket();
                    InetAddress address = InetAddress.getByName(ip != null ? ip : "***************");
                    DatagramPacket udpPacket = new DatagramPacket(packet, packet.length, address, 9);
                    socket.send(udpPacket);
                    socket.close();
                    
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }
            
            @Override
            protected void onPostExecute(Boolean result) {
                promise.resolve(result);
            }
        }.execute();
    }
}
