package com.example.smartdm;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.io.IOException;

public class NetworkToolsModule extends ReactContextBaseJavaModule {
    
    private final ReactApplicationContext reactContext;
    private final ExecutorService executor;
    
    public NetworkToolsModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        this.executor = Executors.newCachedThreadPool();
    }
    
    @Override
    public String getName() {
        return "AndroidNetworkTools";
    }
    
    @ReactMethod
    public void isAvailable(Promise promise) {
        try {
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }
    
    @ReactMethod
    public void scanSubnetDevices(Promise promise) {
        executor.execute(() -> {
            try {
                WritableArray devices = Arguments.createArray();
                
                // 获取本地IP地址
                String localIP = getLocalIPAddress();
                if (localIP == null) {
                    promise.reject("ERROR", "无法获取本地IP地址");
                    return;
                }
                
                // 简单的ping扫描
                String subnet = localIP.substring(0, localIP.lastIndexOf('.'));
                List<String> activeIPs = new ArrayList<>();
                
                for (int i = 1; i <= 254; i++) {
                    String testIP = subnet + "." + i;
                    try {
                        InetAddress address = InetAddress.getByName(testIP);
                        if (address.isReachable(1000)) { // 1秒超时
                            activeIPs.add(testIP);
                        }
                    } catch (IOException e) {
                        // 忽略无法到达的IP
                    }
                }
                
                // 构建结果
                for (String ip : activeIPs) {
                    WritableMap device = Arguments.createMap();
                    device.putString("ip", ip);
                    device.putString("hostname", getHostname(ip));
                    device.putString("mac", "未知"); // 简化版本不获取MAC地址
                    devices.pushMap(device);
                }
                
                promise.resolve(devices);
                
            } catch (Exception e) {
                promise.reject("ERROR", e.getMessage());
            }
        });
    }
    
    @ReactMethod
    public void pingDevice(String ip, Promise promise) {
        executor.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                InetAddress address = InetAddress.getByName(ip);
                boolean isReachable = address.isReachable(5000); // 5秒超时
                long responseTime = System.currentTimeMillis() - startTime;
                
                WritableMap result = Arguments.createMap();
                result.putBoolean("isReachable", isReachable);
                result.putInt("responseTime", (int) responseTime);
                result.putString("ip", ip);
                
                promise.resolve(result);
                
            } catch (Exception e) {
                promise.reject("ERROR", e.getMessage());
            }
        });
    }
    
    @ReactMethod
    public void scanDevicePorts(String ip, Promise promise) {
        executor.execute(() -> {
            try {
                WritableArray openPorts = Arguments.createArray();
                WritableArray services = Arguments.createArray();
                
                // 扫描常见端口
                int[] commonPorts = {21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443};
                
                for (int port : commonPorts) {
                    try {
                        java.net.Socket socket = new java.net.Socket();
                        socket.connect(new java.net.InetSocketAddress(ip, port), 1000);
                        socket.close();
                        
                        openPorts.pushInt(port);
                        
                        WritableMap service = Arguments.createMap();
                        service.putInt("port", port);
                        service.putString("service", getServiceName(port));
                        services.pushMap(service);
                        
                    } catch (IOException e) {
                        // 端口关闭或无法连接
                    }
                }
                
                WritableMap result = Arguments.createMap();
                result.putArray("openPorts", openPorts);
                result.putArray("services", services);
                result.putString("ip", ip);
                
                promise.resolve(result);
                
            } catch (Exception e) {
                promise.reject("ERROR", e.getMessage());
            }
        });
    }
    
    private String getLocalIPAddress() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress() && !addr.isLinkLocalAddress()) {
                        String sAddr = addr.getHostAddress();
                        if (sAddr.indexOf(':') < 0) { // IPv4
                            return sAddr;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }
    
    private String getHostname(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.getHostName();
        } catch (Exception e) {
            return ip; // 如果无法解析主机名，返回IP
        }
    }
    
    private String getServiceName(int port) {
        switch (port) {
            case 21: return "FTP";
            case 22: return "SSH";
            case 23: return "Telnet";
            case 25: return "SMTP";
            case 53: return "DNS";
            case 80: return "HTTP";
            case 110: return "POP3";
            case 143: return "IMAP";
            case 443: return "HTTPS";
            case 993: return "IMAPS";
            case 995: return "POP3S";
            case 8080: return "HTTP-Alt";
            case 8443: return "HTTPS-Alt";
            default: return "Unknown";
        }
    }
}
