-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
MERGED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/5b591e4f61ee80042a9c37b818e3a7bf/transformed/jetified-flipper-network-plugin-0.182.0/AndroidManifest.xml:8:1-15:12
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/0c496f2fc37023df06f9d02d37d0ef91/transformed/jetified-flipper-fresco-plugin-0.182.0/AndroidManifest.xml:8:1-15:12
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:8:1-18:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-10:12
MERGED from [:expo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-19:12
MERGED from [:expo-application] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-blur] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-camera] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:expo-constants] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-36:12
MERGED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-haptics] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:expo-keep-awake] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linear-gradient] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-18:12
MERGED from [:expo-splash-screen] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-system-ui] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-system-ui/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-15:12
MERGED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-18:12
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/e85eaf65c35960fd6c68d84e2f8b9ce5/transformed/jetified-react-android-0.72.10-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/95a326b5ef2b6d652619cb0395723787/transformed/jetified-imagepipeline-okhttp3-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:flipper:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1fa94a31fa4da22d381ea6236d054e2b/transformed/jetified-flipper-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:stetho:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3546878e72fd2604e41352b7f97fede6/transformed/jetified-stetho-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fresco:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/********************************/transformed/jetified-fresco-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/0ff69c881453d7c620bcb8fa1197ea71/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:webpsupport:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/02d1ed52b7caf46213bfdfb0432ce86d/transformed/jetified-webpsupport-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:hermes-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/52d0ff8b254202a36f5bd860de7aa313/transformed/jetified-hermes-android-0.72.10-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/d5f2219cd97b3bdb256f48860266e4f7/transformed/jetified-fragment-ktx-1.5.7/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:14:1-34:12
MERGED from [com.google.android.material:material:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0c75c5f0e7179b7cdabaa27657b2f208/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/63bc111804b72bf8017bac3025a4f451/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/66cdc81fad99534d955af7bdb22c2103/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-location:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/4994a1a0c87f2cda6efc277744eb4395/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/445b365a532be50badaa160c373fe877/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/a03b736c16f5f110a18f4d441b410128/transformed/fragment-1.5.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1ec79621a38e0187c3da6e57d3e969f5/transformed/jetified-activity-1.7.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/6639b2ba47e67f9a171697baff445d6e/transformed/jetified-activity-ktx-1.7.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a24e611e26765f936b92bd999250309c/transformed/jetified-tracing-ktx-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/f29c0c3dc8a469e40e51d9361235fff9/transformed/jetified-appcompat-resources-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/cdcce36addc897b599db36be2676cc38/transformed/jetified-autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/47076c48d62ecb957bfbcd872d8c8df9/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6e91a04551f37af303eb6eb5b1a1d71/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/f8b5ded03cf62f1ab4c0c86fca1db947/transformed/webkit-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e1318b9ede6741210e5286e9f5adc4eb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a755cb40b5b5d96ecd738439052ae52b/transformed/jetified-emoji2-views-helper-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:17:1-37:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/10d01bcce9ab49fa19935b4f26b58f07/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c34acec82227c71a208454226ab8fb65/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ec5afaa49230f9e0b327dfda90519d38/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2680c942b270b94ad03827324e5898f5/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/0757311ae124a351bd7937896345e83e/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0f96ee0d972ed54558a4ed64930d072c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/33b2b23cce8bbc75c6126ddea45b696d/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/3d15046524d1ce7dcc35855dff7e10cf/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4f60a945d4fad6c6f21aae5064f62ce/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d3562b8758a556282414ef48d42a240b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a855445b37ceb48a94d89ad84fdd2407/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/6a4fec2e0cc1fe79529d24c4b319aac9/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/e12abfd6174e25cdba8fa54d54d5f41d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/49a192708c13b16fefba1b7cd3e97af2/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e2b72d0154eed65d85746a830357e622/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e13271277b40d7ef44e3516527ff268f/transformed/jetified-lifecycle-service-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f1bf5b0c5d88e274fd11c8148a45392f/transformed/jetified-lifecycle-livedata-core-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/b85c566507f3fcaa533d150fde169589/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/5b0848cd486e9e71e938badee3ed00d4/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/746b7c6c16f61d67508f2257b09dd0f2/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/52eebbc9eef661340e04462c7160394a/transformed/jetified-lifecycle-viewmodel-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/a6f481c7e26526191d91a3755101c62e/transformed/jetified-lifecycle-runtime-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e3823d95eff5176f1d327a3fd677b45d/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/11efe7002bb92e944a368e1c7b2b0b82/transformed/jetified-core-ktx-1.7.0/AndroidManifest.xml:2:1-9:12
MERGED from [:expo-dev-client] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] /Users/<USER>/.gradle/caches/transforms-3/7361f72570050d0d28ff26a084a4fbc9/transformed/jetified-openssl-1.1.1l-beta-1/AndroidManifest.xml:1:1-3:12
MERGED from [com.facebook.fbjni:fbjni:0.3.0] /Users/<USER>/.gradle/caches/transforms-3/f83b2a8c31d41aa3d574bb043859d188/transformed/jetified-fbjni-0.3.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a84aa7770c5d5f4cf51d3b3396c58137/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/2322109b61a7c5d3d53375c8d8ebbc32/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:drawee:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/4d9037a39c66ac99277e66f6a0cc4eb6/transformed/jetified-drawee-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6144fd18a800b9cd12b998ae836c037c/transformed/jetified-nativeimagefilters-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3b2e9c4d0bf382986fffb4f6ec55e9df/transformed/jetified-memory-type-native-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/9a9ca5d0cc4f0279f33014e8bdb80dfb/transformed/jetified-memory-type-java-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/8767ed6db3d8d62f00093857a20d20f2/transformed/jetified-imagepipeline-native-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:soloader:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/c2cf6ad691162766c925bfcabc8db071/transformed/jetified-soloader-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:2:1-17:12
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/caa6710e9a8fd86be59c9ad9c7994679/transformed/jetified-memory-type-ashmem-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/7303f307ed7c169304d15a0cf62a8979/transformed/jetified-imagepipeline-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/cdb2e0e8bf25ab41fd27b6e22cca2a04/transformed/jetified-nativeimagetranscoder-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/05bac9d51ef02ec7ee9e8089919fced1/transformed/jetified-imagepipeline-base-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/a86894f0b783959b7a820bdfdc85e9c5/transformed/sqlite-framework-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/98f2d555d17caf382b825dc6bff67119/transformed/jetified-tracing-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:middleware:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/2c8437ddd04304ed8f4482f505f51f95/transformed/jetified-middleware-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:ui-common:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1f53ccd0425e4c215b2199f6794f825f/transformed/jetified-ui-common-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.fresco:fbcore:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6e5f5ef233d0979d45f80c77da381b42/transformed/jetified-fbcore-2.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d16988d97a62b4d3fabfca8be53980bf/transformed/sqlite-2.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf30d74540bd247bcbf8a1eef27435b2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/688b8516633053444cdc9a9d666f140a/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.3] /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/042050f39c22b1a31daf3ff0a977d3c0/transformed/exifinterface-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/df0d95e521996350a2bbb62495827249/transformed/jetified-viewbinding-7.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/776760f8b8843df8540469d1cab09728/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/16f91ce3b7e0eae3f8b5e08bd0068298/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7a9b32914eb3e9306a6869561f4ebb0f/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/46c8d5fd215cc304798b274bac6102b5/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/13b6dcf88da4d87f98cad43e62c00c7f/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/e2dd0b54069362782fc1d8121532d2fa/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:2:1-13:12
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:2:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/50c292fcce98fc1a2b8d4e149715ac2f/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:17:1-24:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:1:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:1-43:12
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:2:3-78
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-81
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-81
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:12:5-81
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:12:5-81
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:3:3-76
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-79
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-79
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:14:5-79
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:14:5-79
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:3:20-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:4:3-76
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-79
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-79
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-79
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:13:5-79
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:13:5-79
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:4:20-74
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:5:3-73
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:16:5-76
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:16:5-76
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-76
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-76
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-76
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-76
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:5:20-71
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:6:3-62
MERGED from [:expo-camera] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-65
MERGED from [:expo-camera] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-65
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:23:5-65
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:23:5-65
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:6:20-60
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:7:3-73
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:7:20-71
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:8:3-74
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-77
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-77
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:8:20-72
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:9:3-64
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:15:5-67
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:15:5-67
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-67
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:9:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:10:3-77
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:10:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:11:3-68
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:11:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:3-75
MERGED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:3-75
MERGED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:3-75
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:13:3-63
MERGED from [:expo-haptics] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-66
MERGED from [:expo-haptics] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-66
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:13:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:14:3-78
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:14:20-76
queries
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:15:3-21:13
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-9:15
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:5-18:15
MERGED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:16:5-20:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:7-58
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:7-67
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:17-65
data
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
	android:scheme
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
application
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:3-42:17
MERGED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:3-42:17
MERGED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:3-42:17
MERGED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-17:19
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-34:19
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-34:19
MERGED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-22:19
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:5-31:19
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-16:19
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:32:5-20
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:32:5-20
MERGED from [com.google.android.material:material:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/AndroidManifest.xml:22:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0c75c5f0e7179b7cdabaa27657b2f208/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0c75c5f0e7179b7cdabaa27657b2f208/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-location:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/4994a1a0c87f2cda6efc277744eb4395/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/4994a1a0c87f2cda6efc277744eb4395/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/445b365a532be50badaa160c373fe877/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/445b365a532be50badaa160c373fe877/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:11:5-15:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.3] /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.3] /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/776760f8b8843df8540469d1cab09728/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/776760f8b8843df8540469d1cab09728/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:11:5-20
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:18:5-28:19
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:18:5-28:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:24:18-86
	android:label
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:48-80
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:48-80
	tools:ignore
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:116-161
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:81-115
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:162-188
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:162-188
	android:theme
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:189-220
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:189-220
	tools:replace
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:16-47
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:5-83
	android:value
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:60-81
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:16-59
meta-data#expo.modules.updates.EXPO_SDK_VERSION
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:5-93
	android:value
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:69-91
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:16-68
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:5-105
	android:value
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:81-103
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:5-99
	android:value
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:80-97
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:16-79
activity#com.example.smartdm.MainActivity
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:5-40:16
	android:screenOrientation
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:300-336
	android:label
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:44-76
	android:launchMode
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:155-186
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:187-229
	android:exported
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:276-299
	android:configChanges
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:77-154
	android:theme
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:230-275
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:28:7-31:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:29:9-60
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:29:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:30:9-68
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:30:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.example.smartdm+data:scheme:exp+smartdm+data:scheme:smartdm
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:32:7-39:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:9-67
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:19-65
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:5-106
	android:exported
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:80-104
	android:name
		ADDED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:15-79
uses-sdk
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/5b591e4f61ee80042a9c37b818e3a7bf/transformed/jetified-flipper-network-plugin-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-network-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/5b591e4f61ee80042a9c37b818e3a7bf/transformed/jetified-flipper-network-plugin-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/0c496f2fc37023df06f9d02d37d0ef91/transformed/jetified-flipper-fresco-plugin-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper-fresco-plugin:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/0c496f2fc37023df06f9d02d37d0ef91/transformed/jetified-flipper-fresco-plugin-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [com.facebook.flipper:flipper:0.182.0] /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/AndroidManifest.xml:11:5-13:41
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_netinfo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-network-info] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-application] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-application] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-application/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-blur] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-blur/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-camera] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:expo-font] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-font] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-font/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-haptics] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-keep-awake/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-linear-gradient/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-splash-screen] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-splash-screen/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-system-ui/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-system-ui] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-system-ui/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/e85eaf65c35960fd6c68d84e2f8b9ce5/transformed/jetified-react-android-0.72.10-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/e85eaf65c35960fd6c68d84e2f8b9ce5/transformed/jetified-react-android-0.72.10-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/95a326b5ef2b6d652619cb0395723787/transformed/jetified-imagepipeline-okhttp3-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/95a326b5ef2b6d652619cb0395723787/transformed/jetified-imagepipeline-okhttp3-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:flipper:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1fa94a31fa4da22d381ea6236d054e2b/transformed/jetified-flipper-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:flipper:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1fa94a31fa4da22d381ea6236d054e2b/transformed/jetified-flipper-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:stetho:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3546878e72fd2604e41352b7f97fede6/transformed/jetified-stetho-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:stetho:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3546878e72fd2604e41352b7f97fede6/transformed/jetified-stetho-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/********************************/transformed/jetified-fresco-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fresco:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/********************************/transformed/jetified-fresco-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/0ff69c881453d7c620bcb8fa1197ea71/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-gif:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/0ff69c881453d7c620bcb8fa1197ea71/transformed/jetified-animated-gif-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/02d1ed52b7caf46213bfdfb0432ce86d/transformed/jetified-webpsupport-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:webpsupport:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/02d1ed52b7caf46213bfdfb0432ce86d/transformed/jetified-webpsupport-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.react:hermes-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/52d0ff8b254202a36f5bd860de7aa313/transformed/jetified-hermes-android-0.72.10-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.72.10] /Users/<USER>/.gradle/caches/transforms-3/52d0ff8b254202a36f5bd860de7aa313/transformed/jetified-hermes-android-0.72.10-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/d5f2219cd97b3bdb256f48860266e4f7/transformed/jetified-fragment-ktx-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/d5f2219cd97b3bdb256f48860266e4f7/transformed/jetified-fragment-ktx-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.material:material:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0c75c5f0e7179b7cdabaa27657b2f208/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/0c75c5f0e7179b7cdabaa27657b2f208/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/63bc111804b72bf8017bac3025a4f451/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/63bc111804b72bf8017bac3025a4f451/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/66cdc81fad99534d955af7bdb22c2103/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/66cdc81fad99534d955af7bdb22c2103/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-location:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/4994a1a0c87f2cda6efc277744eb4395/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:20.0.0] /Users/<USER>/.gradle/caches/transforms-3/4994a1a0c87f2cda6efc277744eb4395/transformed/jetified-play-services-location-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/445b365a532be50badaa160c373fe877/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/445b365a532be50badaa160c373fe877/transformed/jetified-play-services-tasks-18.0.1/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/a03b736c16f5f110a18f4d441b410128/transformed/fragment-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] /Users/<USER>/.gradle/caches/transforms-3/a03b736c16f5f110a18f4d441b410128/transformed/fragment-1.5.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1ec79621a38e0187c3da6e57d3e969f5/transformed/jetified-activity-1.7.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1ec79621a38e0187c3da6e57d3e969f5/transformed/jetified-activity-1.7.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/6639b2ba47e67f9a171697baff445d6e/transformed/jetified-activity-ktx-1.7.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/6639b2ba47e67f9a171697baff445d6e/transformed/jetified-activity-ktx-1.7.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a24e611e26765f936b92bd999250309c/transformed/jetified-tracing-ktx-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/a24e611e26765f936b92bd999250309c/transformed/jetified-tracing-ktx-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/f29c0c3dc8a469e40e51d9361235fff9/transformed/jetified-appcompat-resources-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/f29c0c3dc8a469e40e51d9361235fff9/transformed/jetified-appcompat-resources-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/cdcce36addc897b599db36be2676cc38/transformed/jetified-autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/cdcce36addc897b599db36be2676cc38/transformed/jetified-autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/47076c48d62ecb957bfbcd872d8c8df9/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/47076c48d62ecb957bfbcd872d8c8df9/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6e91a04551f37af303eb6eb5b1a1d71/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d6e91a04551f37af303eb6eb5b1a1d71/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/f8b5ded03cf62f1ab4c0c86fca1db947/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/f8b5ded03cf62f1ab4c0c86fca1db947/transformed/webkit-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e1318b9ede6741210e5286e9f5adc4eb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/e1318b9ede6741210e5286e9f5adc4eb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a755cb40b5b5d96ecd738439052ae52b/transformed/jetified-emoji2-views-helper-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a755cb40b5b5d96ecd738439052ae52b/transformed/jetified-emoji2-views-helper-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/10d01bcce9ab49fa19935b4f26b58f07/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/10d01bcce9ab49fa19935b4f26b58f07/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c34acec82227c71a208454226ab8fb65/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/c34acec82227c71a208454226ab8fb65/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ec5afaa49230f9e0b327dfda90519d38/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/ec5afaa49230f9e0b327dfda90519d38/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2680c942b270b94ad03827324e5898f5/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2680c942b270b94ad03827324e5898f5/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/0757311ae124a351bd7937896345e83e/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/0757311ae124a351bd7937896345e83e/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0f96ee0d972ed54558a4ed64930d072c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0f96ee0d972ed54558a4ed64930d072c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/33b2b23cce8bbc75c6126ddea45b696d/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/33b2b23cce8bbc75c6126ddea45b696d/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/3d15046524d1ce7dcc35855dff7e10cf/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/3d15046524d1ce7dcc35855dff7e10cf/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4f60a945d4fad6c6f21aae5064f62ce/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/c4f60a945d4fad6c6f21aae5064f62ce/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d3562b8758a556282414ef48d42a240b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/d3562b8758a556282414ef48d42a240b/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a855445b37ceb48a94d89ad84fdd2407/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/a855445b37ceb48a94d89ad84fdd2407/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/6a4fec2e0cc1fe79529d24c4b319aac9/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/6a4fec2e0cc1fe79529d24c4b319aac9/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/e12abfd6174e25cdba8fa54d54d5f41d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/e12abfd6174e25cdba8fa54d54d5f41d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/49a192708c13b16fefba1b7cd3e97af2/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/49a192708c13b16fefba1b7cd3e97af2/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e2b72d0154eed65d85746a830357e622/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e2b72d0154eed65d85746a830357e622/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e13271277b40d7ef44e3516527ff268f/transformed/jetified-lifecycle-service-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e13271277b40d7ef44e3516527ff268f/transformed/jetified-lifecycle-service-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f1bf5b0c5d88e274fd11c8148a45392f/transformed/jetified-lifecycle-livedata-core-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/f1bf5b0c5d88e274fd11c8148a45392f/transformed/jetified-lifecycle-livedata-core-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/b85c566507f3fcaa533d150fde169589/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/b85c566507f3fcaa533d150fde169589/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/5b0848cd486e9e71e938badee3ed00d4/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/5b0848cd486e9e71e938badee3ed00d4/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/746b7c6c16f61d67508f2257b09dd0f2/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/746b7c6c16f61d67508f2257b09dd0f2/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/52eebbc9eef661340e04462c7160394a/transformed/jetified-lifecycle-viewmodel-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/52eebbc9eef661340e04462c7160394a/transformed/jetified-lifecycle-viewmodel-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/a6f481c7e26526191d91a3755101c62e/transformed/jetified-lifecycle-runtime-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/a6f481c7e26526191d91a3755101c62e/transformed/jetified-lifecycle-runtime-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e3823d95eff5176f1d327a3fd677b45d/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/e3823d95eff5176f1d327a3fd677b45d/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/11efe7002bb92e944a368e1c7b2b0b82/transformed/jetified-core-ktx-1.7.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/11efe7002bb92e944a368e1c7b2b0b82/transformed/jetified-core-ktx-1.7.0/AndroidManifest.xml:5:5-7:41
MERGED from [:expo-dev-client] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] /Users/<USER>/.gradle/caches/transforms-3/7361f72570050d0d28ff26a084a4fbc9/transformed/jetified-openssl-1.1.1l-beta-1/AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] /Users/<USER>/.gradle/caches/transforms-3/7361f72570050d0d28ff26a084a4fbc9/transformed/jetified-openssl-1.1.1l-beta-1/AndroidManifest.xml:2:2-70
MERGED from [com.facebook.fbjni:fbjni:0.3.0] /Users/<USER>/.gradle/caches/transforms-3/f83b2a8c31d41aa3d574bb043859d188/transformed/jetified-fbjni-0.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fbjni:fbjni:0.3.0] /Users/<USER>/.gradle/caches/transforms-3/f83b2a8c31d41aa3d574bb043859d188/transformed/jetified-fbjni-0.3.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a84aa7770c5d5f4cf51d3b3396c58137/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-base:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/a84aa7770c5d5f4cf51d3b3396c58137/transformed/jetified-animated-base-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/2322109b61a7c5d3d53375c8d8ebbc32/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:animated-drawable:2.5.0] /Users/<USER>/.gradle/caches/transforms-3/2322109b61a7c5d3d53375c8d8ebbc32/transformed/jetified-animated-drawable-2.5.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/4d9037a39c66ac99277e66f6a0cc4eb6/transformed/jetified-drawee-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:drawee:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/4d9037a39c66ac99277e66f6a0cc4eb6/transformed/jetified-drawee-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6144fd18a800b9cd12b998ae836c037c/transformed/jetified-nativeimagefilters-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagefilters:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6144fd18a800b9cd12b998ae836c037c/transformed/jetified-nativeimagefilters-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3b2e9c4d0bf382986fffb4f6ec55e9df/transformed/jetified-memory-type-native-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/3b2e9c4d0bf382986fffb4f6ec55e9df/transformed/jetified-memory-type-native-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/9a9ca5d0cc4f0279f33014e8bdb80dfb/transformed/jetified-memory-type-java-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-java:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/9a9ca5d0cc4f0279f33014e8bdb80dfb/transformed/jetified-memory-type-java-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/8767ed6db3d8d62f00093857a20d20f2/transformed/jetified-imagepipeline-native-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-native:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/8767ed6db3d8d62f00093857a20d20f2/transformed/jetified-imagepipeline-native-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/c2cf6ad691162766c925bfcabc8db071/transformed/jetified-soloader-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:soloader:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/c2cf6ad691162766c925bfcabc8db071/transformed/jetified-soloader-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/caa6710e9a8fd86be59c9ad9c7994679/transformed/jetified-memory-type-ashmem-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:memory-type-ashmem:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/caa6710e9a8fd86be59c9ad9c7994679/transformed/jetified-memory-type-ashmem-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/7303f307ed7c169304d15a0cf62a8979/transformed/jetified-imagepipeline-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/7303f307ed7c169304d15a0cf62a8979/transformed/jetified-imagepipeline-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/cdb2e0e8bf25ab41fd27b6e22cca2a04/transformed/jetified-nativeimagetranscoder-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:nativeimagetranscoder:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/cdb2e0e8bf25ab41fd27b6e22cca2a04/transformed/jetified-nativeimagetranscoder-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/05bac9d51ef02ec7ee9e8089919fced1/transformed/jetified-imagepipeline-base-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:imagepipeline-base:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/05bac9d51ef02ec7ee9e8089919fced1/transformed/jetified-imagepipeline-base-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/a86894f0b783959b7a820bdfdc85e9c5/transformed/sqlite-framework-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/a86894f0b783959b7a820bdfdc85e9c5/transformed/sqlite-framework-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/98f2d555d17caf382b825dc6bff67119/transformed/jetified-tracing-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/98f2d555d17caf382b825dc6bff67119/transformed/jetified-tracing-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:middleware:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/2c8437ddd04304ed8f4482f505f51f95/transformed/jetified-middleware-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:middleware:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/2c8437ddd04304ed8f4482f505f51f95/transformed/jetified-middleware-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1f53ccd0425e4c215b2199f6794f825f/transformed/jetified-ui-common-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:ui-common:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/1f53ccd0425e4c215b2199f6794f825f/transformed/jetified-ui-common-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6e5f5ef233d0979d45f80c77da381b42/transformed/jetified-fbcore-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.facebook.fresco:fbcore:2.6.0] /Users/<USER>/.gradle/caches/transforms-3/6e5f5ef233d0979d45f80c77da381b42/transformed/jetified-fbcore-2.6.0/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d16988d97a62b4d3fabfca8be53980bf/transformed/sqlite-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/d16988d97a62b4d3fabfca8be53980bf/transformed/sqlite-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf30d74540bd247bcbf8a1eef27435b2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf30d74540bd247bcbf8a1eef27435b2/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/688b8516633053444cdc9a9d666f140a/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/688b8516633053444cdc9a9d666f140a/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.3] /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.3] /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/042050f39c22b1a31daf3ff0a977d3c0/transformed/exifinterface-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/042050f39c22b1a31daf3ff0a977d3c0/transformed/exifinterface-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/df0d95e521996350a2bbb62495827249/transformed/jetified-viewbinding-7.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] /Users/<USER>/.gradle/caches/transforms-3/df0d95e521996350a2bbb62495827249/transformed/jetified-viewbinding-7.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/776760f8b8843df8540469d1cab09728/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/776760f8b8843df8540469d1cab09728/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/16f91ce3b7e0eae3f8b5e08bd0068298/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/16f91ce3b7e0eae3f8b5e08bd0068298/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7a9b32914eb3e9306a6869561f4ebb0f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/7a9b32914eb3e9306a6869561f4ebb0f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/46c8d5fd215cc304798b274bac6102b5/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/46c8d5fd215cc304798b274bac6102b5/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/13b6dcf88da4d87f98cad43e62c00c7f/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/13b6dcf88da4d87f98cad43e62c00c7f/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/e2dd0b54069362782fc1d8121532d2fa/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/e2dd0b54069362782fc1d8121532d2fa/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:5:5-7:41
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:8:5-10:41
MERGED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:8:5-10:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/50c292fcce98fc1a2b8d4e149715ac2f/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/50c292fcce98fc1a2b8d4e149715ac2f/transformed/jetified-annotation-experimental-1.1.0/AndroidManifest.xml:20:5-22:41
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml
		INJECTED from /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:17-67
package#host.exp.exponent
ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:18-50
meta-data#expo.modules.updates.AUTO_SETUP
ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-59
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:9-29:20
	android:launchMode
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-44
	android:exported
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-36
	android:theme
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-70
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-28:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:30:9-33:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:32:13-49
	android:theme
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:33:13-67
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:31:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-20:29
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-74
service#expo.modules.location.services.LocationTaskService
ADDED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-15:56
	android:exported
		ADDED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-37
	android:foregroundServiceType
		ADDED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-53
	android:name
		ADDED from [:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-78
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.10.5] /Users/<USER>/.gradle/caches/transforms-3/6efc14123ea8dbcef69fc56f1ec4ce2b/transformed/jetified-soloader-0.10.5/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-57
uses-feature#android.hardware.camera
ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:27:9-33
	android:name
		ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:26:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:29:9-57
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:9:22-107
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:15:5-98
	android:name
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:15:22-95
uses-permission#com.google.android.gms.permission.ACTIVITY_RECOGNITION
ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:16:5-94
	android:name
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:16:22-91
service#io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService
ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:20:13-132
service#io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService
ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:22:9-24:40
	android:exported
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:24:13-37
	android:name
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:23:13-127
service#io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService
ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:25:9-27:40
	android:exported
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:26:13-120
