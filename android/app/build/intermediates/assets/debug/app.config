{"name": "smartdm", "slug": "smartdm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/images/icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.example.smartdm"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.example.smartdm", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-dev-client"], "scheme": "smartdm", "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "921dc956-7c4f-495b-adff-414efbba36d8"}}, "sdkVersion": "49.0.0", "platforms": ["ios", "android", "web"]}