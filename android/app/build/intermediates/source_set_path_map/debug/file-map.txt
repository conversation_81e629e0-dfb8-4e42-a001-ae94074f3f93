com.example.smartdm.app-recyclerview-1.1.0-0 /Users/<USER>/.gradle/caches/transforms-3/0757311ae124a351bd7937896345e83e/transformed/recyclerview-1.1.0/res
com.example.smartdm.app-jetified-core-ktx-1.7.0-1 /Users/<USER>/.gradle/caches/transforms-3/11efe7002bb92e944a368e1c7b2b0b82/transformed/jetified-core-ktx-1.7.0/res
com.example.smartdm.app-cardview-1.0.0-2 /Users/<USER>/.gradle/caches/transforms-3/16f91ce3b7e0eae3f8b5e08bd0068298/transformed/cardview-1.0.0/res
com.example.smartdm.app-jetified-activity-1.7.1-3 /Users/<USER>/.gradle/caches/transforms-3/1ec79621a38e0187c3da6e57d3e969f5/transformed/jetified-activity-1.7.1/res
com.example.smartdm.app-jetified-cameraview-1.0.0-4 /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/res
com.example.smartdm.app-material-1.2.1-5 /Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/res
com.example.smartdm.app-transition-1.2.0-6 /Users/<USER>/.gradle/caches/transforms-3/3d15046524d1ce7dcc35855dff7e10cf/transformed/transition-1.2.0/res
com.example.smartdm.app-jetified-lifecycle-process-2.6.1-7 /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/res
com.example.smartdm.app-jetified-flipper-0.182.0-8 /Users/<USER>/.gradle/caches/transforms-3/48a6fe5118d07d1c939ad9d34adada86/transformed/jetified-flipper-0.182.0/res
com.example.smartdm.app-jetified-savedstate-1.2.1-9 /Users/<USER>/.gradle/caches/transforms-3/49a192708c13b16fefba1b7cd3e97af2/transformed/jetified-savedstate-1.2.1/res
com.example.smartdm.app-jetified-drawee-2.6.0-10 /Users/<USER>/.gradle/caches/transforms-3/4d9037a39c66ac99277e66f6a0cc4eb6/transformed/jetified-drawee-2.6.0/res
com.example.smartdm.app-jetified-annotation-experimental-1.1.0-11 /Users/<USER>/.gradle/caches/transforms-3/50c292fcce98fc1a2b8d4e149715ac2f/transformed/jetified-annotation-experimental-1.1.0/res
com.example.smartdm.app-jetified-lifecycle-viewmodel-ktx-2.6.1-12 /Users/<USER>/.gradle/caches/transforms-3/52eebbc9eef661340e04462c7160394a/transformed/jetified-lifecycle-viewmodel-ktx-2.6.1/res
com.example.smartdm.app-lifecycle-viewmodel-2.6.1-13 /Users/<USER>/.gradle/caches/transforms-3/5b0848cd486e9e71e938badee3ed00d4/transformed/lifecycle-viewmodel-2.6.1/res
com.example.smartdm.app-browser-1.2.0-14 /Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/res
com.example.smartdm.app-jetified-activity-ktx-1.7.1-15 /Users/<USER>/.gradle/caches/transforms-3/6639b2ba47e67f9a171697baff445d6e/transformed/jetified-activity-ktx-1.7.1/res
com.example.smartdm.app-jetified-viewpager2-1.0.0-16 /Users/<USER>/.gradle/caches/transforms-3/66cdc81fad99534d955af7bdb22c2103/transformed/jetified-viewpager2-1.0.0/res
com.example.smartdm.app-lifecycle-runtime-2.6.1-17 /Users/<USER>/.gradle/caches/transforms-3/746b7c6c16f61d67508f2257b09dd0f2/transformed/lifecycle-runtime-2.6.1/res
com.example.smartdm.app-appcompat-1.4.1-18 /Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/res
com.example.smartdm.app-jetified-tracing-1.1.0-19 /Users/<USER>/.gradle/caches/transforms-3/98f2d555d17caf382b825dc6bff67119/transformed/jetified-tracing-1.1.0/res
com.example.smartdm.app-jetified-startup-runtime-1.1.1-20 /Users/<USER>/.gradle/caches/transforms-3/9a5a7952d35207de861d4321192ad9ec/transformed/jetified-startup-runtime-1.1.1/res
com.example.smartdm.app-fragment-1.5.7-21 /Users/<USER>/.gradle/caches/transforms-3/a03b736c16f5f110a18f4d441b410128/transformed/fragment-1.5.7/res
com.example.smartdm.app-jetified-tracing-ktx-1.1.0-22 /Users/<USER>/.gradle/caches/transforms-3/a24e611e26765f936b92bd999250309c/transformed/jetified-tracing-ktx-1.1.0/res
com.example.smartdm.app-jetified-lifecycle-runtime-ktx-2.6.1-23 /Users/<USER>/.gradle/caches/transforms-3/a6f481c7e26526191d91a3755101c62e/transformed/jetified-lifecycle-runtime-ktx-2.6.1/res
com.example.smartdm.app-jetified-emoji2-views-helper-1.0.0-24 /Users/<USER>/.gradle/caches/transforms-3/a755cb40b5b5d96ecd738439052ae52b/transformed/jetified-emoji2-views-helper-1.0.0/res
com.example.smartdm.app-sqlite-framework-2.2.0-25 /Users/<USER>/.gradle/caches/transforms-3/a86894f0b783959b7a820bdfdc85e9c5/transformed/sqlite-framework-2.2.0/res
com.example.smartdm.app-jetified-profileinstaller-1.3.0-26 /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/res
com.example.smartdm.app-lifecycle-livedata-2.6.1-27 /Users/<USER>/.gradle/caches/transforms-3/b85c566507f3fcaa533d150fde169589/transformed/lifecycle-livedata-2.6.1/res
com.example.smartdm.app-media-1.0.0-28 /Users/<USER>/.gradle/caches/transforms-3/c4f60a945d4fad6c6f21aae5064f62ce/transformed/media-1.0.0/res
com.example.smartdm.app-jetified-play-services-base-18.0.1-29 /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/res
com.example.smartdm.app-jetified-BlurView-version-2.0.3-30 /Users/<USER>/.gradle/caches/transforms-3/cb19b1de079dbb44deebd65fe0f1a657/transformed/jetified-BlurView-version-2.0.3/res
com.example.smartdm.app-jetified-autofill-1.1.0-31 /Users/<USER>/.gradle/caches/transforms-3/cdcce36addc897b599db36be2676cc38/transformed/jetified-autofill-1.1.0/res
com.example.smartdm.app-sqlite-2.2.0-32 /Users/<USER>/.gradle/caches/transforms-3/d16988d97a62b4d3fabfca8be53980bf/transformed/sqlite-2.2.0/res
com.example.smartdm.app-core-1.8.0-33 /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/res
com.example.smartdm.app-jetified-fragment-ktx-1.5.7-34 /Users/<USER>/.gradle/caches/transforms-3/d5f2219cd97b3bdb256f48860266e4f7/transformed/jetified-fragment-ktx-1.5.7/res
com.example.smartdm.app-jetified-savedstate-ktx-1.2.1-35 /Users/<USER>/.gradle/caches/transforms-3/e12abfd6174e25cdba8fa54d54d5f41d/transformed/jetified-savedstate-ktx-1.2.1/res
com.example.smartdm.app-jetified-lifecycle-service-2.6.1-36 /Users/<USER>/.gradle/caches/transforms-3/e13271277b40d7ef44e3516527ff268f/transformed/jetified-lifecycle-service-2.6.1/res
com.example.smartdm.app-jetified-play-services-basement-18.0.0-37 /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/res
com.example.smartdm.app-lifecycle-livedata-core-2.6.1-38 /Users/<USER>/.gradle/caches/transforms-3/e2b72d0154eed65d85746a830357e622/transformed/lifecycle-livedata-core-2.6.1/res
com.example.smartdm.app-core-runtime-2.2.0-39 /Users/<USER>/.gradle/caches/transforms-3/e2dd0b54069362782fc1d8121532d2fa/transformed/core-runtime-2.2.0/res
com.example.smartdm.app-jetified-lifecycle-viewmodel-savedstate-2.6.1-40 /Users/<USER>/.gradle/caches/transforms-3/e3823d95eff5176f1d327a3fd677b45d/transformed/jetified-lifecycle-viewmodel-savedstate-2.6.1/res
com.example.smartdm.app-jetified-react-android-0.72.10-debug-41 /Users/<USER>/.gradle/caches/transforms-3/e85eaf65c35960fd6c68d84e2f8b9ce5/transformed/jetified-react-android-0.72.10-debug/res
com.example.smartdm.app-coordinatorlayout-1.2.0-42 /Users/<USER>/.gradle/caches/transforms-3/ec5afaa49230f9e0b327dfda90519d38/transformed/coordinatorlayout-1.2.0/res
com.example.smartdm.app-jetified-emoji2-1.0.0-43 /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/res
com.example.smartdm.app-jetified-lifecycle-livedata-core-ktx-2.6.1-44 /Users/<USER>/.gradle/caches/transforms-3/f1bf5b0c5d88e274fd11c8148a45392f/transformed/jetified-lifecycle-livedata-core-ktx-2.6.1/res
com.example.smartdm.app-jetified-appcompat-resources-1.4.1-45 /Users/<USER>/.gradle/caches/transforms-3/f29c0c3dc8a469e40e51d9361235fff9/transformed/jetified-appcompat-resources-1.4.1/res
com.example.smartdm.app-pngs-46 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/generated/res/pngs/debug
com.example.smartdm.app-resValues-47 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/generated/res/resValues/debug
com.example.smartdm.app-rs-48 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/generated/res/rs/debug
com.example.smartdm.app-mergeDebugResources-49 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/intermediates/incremental/debug/mergeDebugResources/merged.dir
com.example.smartdm.app-mergeDebugResources-50 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/intermediates/incremental/debug/mergeDebugResources/stripped.dir
com.example.smartdm.app-merged_res-51 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/build/intermediates/merged_res/debug
com.example.smartdm.app-debug-52 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/res
com.example.smartdm.app-main-53 /Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/res
com.example.smartdm.app-packaged_res-54 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-55 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/@react-native-community/netinfo/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-56 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-application/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-57 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-blur/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-58 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-camera/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-59 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-constants/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-60 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-client/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-61 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-62 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu-interface/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-63 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-64 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-65 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-font/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-66 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-haptics/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-67 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-json-utils/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-68 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-keep-awake/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-69 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-linear-gradient/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-70 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-71 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-manifests/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-72 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-73 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-splash-screen/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-74 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-system-ui/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-75 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-updates-interface/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-76 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-77 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-78 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-gesture-handler/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-79 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-network-info/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-80 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-reanimated/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-81 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-safe-area-context/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-82 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-83 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-svg/android/build/intermediates/packaged_res/debug
com.example.smartdm.app-packaged_res-84 /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/packaged_res/debug
