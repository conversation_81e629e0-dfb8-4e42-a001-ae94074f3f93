1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.smartdm"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:3-75
11-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:12:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:2:3-78
12-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:3:3-76
13-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:4:3-76
14-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
15-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:5:3-73
15-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:5:20-71
16    <uses-permission android:name="android.permission.CAMERA" />
16-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:6:3-62
16-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:6:20-60
17    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
17-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:7:3-73
17-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:7:20-71
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:8:3-74
18-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:8:20-72
19    <uses-permission android:name="android.permission.INTERNET" />
19-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:9:3-64
19-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:9:20-62
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:10:3-77
20-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:10:20-75
21    <uses-permission android:name="android.permission.RECORD_AUDIO" />
21-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:11:3-68
21-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:11:20-66
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:13:3-63
22-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:13:20-61
23    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
23-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:14:3-78
23-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:14:20-76
24
25    <queries>
25-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:15:3-21:13
26        <intent>
26-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:16:5-20:14
27            <action android:name="android.intent.action.VIEW" />
27-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:7-58
27-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:15-56
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:7-67
29-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:17-65
30
31            <data android:scheme="https" />
31-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
31-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
32        </intent>
33
34        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
34-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-53
34-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:18-50
35        <intent>
35-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:9-17:18
36            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
36-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-79
36-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:21-76
37        </intent>
38        <intent>
38-->[:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:18
39
40            <!-- Required for opening tabs if targeting API 30 -->
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-90
41-->[:expo-web-browser] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-web-browser/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:21-87
42        </intent>
43    </queries>
44
45    <uses-feature
45-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:25:5-27:36
46        android:name="android.hardware.camera"
46-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:26:9-47
47        android:required="false" />
47-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:27:9-33
48    <uses-feature
48-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:28:5-30:36
49        android:name="android.hardware.camera.autofocus"
49-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:29:9-57
50        android:required="false" />
50-->[com.google.android:cameraview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2471d381e659fa3c34cd9f163f6f6068/transformed/jetified-cameraview-1.0.0/AndroidManifest.xml:30:9-33
51
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:9:5-110
52-->[com.android.installreferrer:installreferrer:1.0] /Users/<USER>/.gradle/caches/transforms-3/d9c3b01863f9310e2e888e4e2e84eb9b/transformed/jetified-installreferrer-1.0/AndroidManifest.xml:9:22-107
53    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
53-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:15:5-98
53-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:15:22-95
54    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
54-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:16:5-94
54-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:16:22-91
55
56    <application
56-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:3-42:17
57        android:name="com.example.smartdm.MainApplication"
57-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:16-47
58        android:allowBackup="true"
58-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:162-188
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/AndroidManifest.xml:24:18-86
60        android:debuggable="true"
61        android:icon="@mipmap/ic_launcher"
61-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:81-115
62        android:label="@string/app_name"
62-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:48-80
63        android:roundIcon="@mipmap/ic_launcher_round"
63-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:116-161
64        android:theme="@style/AppTheme"
64-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:22:189-220
65        android:usesCleartextTraffic="true" >
65-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/debug/AndroidManifest.xml:6:18-53
66        <meta-data
66-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:5-83
67            android:name="expo.modules.updates.ENABLED"
67-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:16-59
68            android:value="false" />
68-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:23:60-81
69        <meta-data
69-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:5-93
70            android:name="expo.modules.updates.EXPO_SDK_VERSION"
70-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:16-68
71            android:value="49.0.0" />
71-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:24:69-91
72        <meta-data
72-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:5-105
73            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
73-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:16-80
74            android:value="ALWAYS" />
74-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:25:81-103
75        <meta-data
75-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:5-99
76            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
76-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:16-79
77            android:value="0" />
77-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:26:80-97
78
79        <activity
79-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:5-40:16
80            android:name="com.example.smartdm.MainActivity"
80-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:15-43
81            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
81-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:77-154
82            android:exported="true"
82-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:276-299
83            android:label="@string/app_name"
83-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:44-76
84            android:launchMode="singleTask"
84-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:155-186
85            android:screenOrientation="portrait"
85-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:300-336
86            android:theme="@style/Theme.App.SplashScreen"
86-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:230-275
87            android:windowSoftInputMode="adjustResize" >
87-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:27:187-229
88            <intent-filter>
88-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:28:7-31:23
89                <action android:name="android.intent.action.MAIN" />
89-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:29:9-60
89-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:29:17-58
90
91                <category android:name="android.intent.category.LAUNCHER" />
91-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:30:9-68
91-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:30:19-66
92            </intent-filter>
93            <intent-filter>
93-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:32:7-39:23
94                <action android:name="android.intent.action.VIEW" />
94-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:7-58
94-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:15-56
95
96                <category android:name="android.intent.category.DEFAULT" />
96-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:9-67
96-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:19-65
97                <category android:name="android.intent.category.BROWSABLE" />
97-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:7-67
97-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:17-65
98
99                <data android:scheme="smartdm" />
99-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
99-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
100                <data android:scheme="com.example.smartdm" />
100-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
100-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
101                <data android:scheme="exp+smartdm" />
101-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
101-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
102            </intent-filter>
103        </activity>
104        <activity
104-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:5-106
105            android:name="com.facebook.react.devsupport.DevSettingsActivity"
105-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:15-79
106            android:exported="false" />
106-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:41:80-104
107
108        <provider
108-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-16:20
109            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
109-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-83
110            android:authorities="com.example.smartdm.fileprovider"
110-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-64
111            android:exported="false"
111-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-37
112            android:grantUriPermissions="true" >
112-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-47
113            <meta-data
113-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-15:63
114                android:name="android.support.FILE_PROVIDER_PATHS"
114-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:17-67
115                android:resource="@xml/file_provider_paths" />
115-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-60
116        </provider>
117
118        <meta-data
118-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-14:37
119            android:name="expo.modules.updates.AUTO_SETUP"
119-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-59
120            android:value="false" />
120-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-34
121
122        <activity
122-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:16:9-29:20
123            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
123-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-81
124            android:exported="true"
124-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:18:13-36
125            android:launchMode="singleTask"
125-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-44
126            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
126-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-70
127            <intent-filter>
127-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-28:29
128                <action android:name="android.intent.action.VIEW" />
128-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:7-58
128-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:15-56
129
130                <category android:name="android.intent.category.DEFAULT" />
130-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:9-67
130-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:19-65
131                <category android:name="android.intent.category.BROWSABLE" />
131-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:7-67
131-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:17-65
132
133                <data android:scheme="expo-dev-launcher" />
133-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
133-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
134            </intent-filter>
135        </activity>
136        <activity
136-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:30:9-33:70
137            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
137-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:31:13-93
138            android:screenOrientation="portrait"
138-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:32:13-49
139            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
139-->[:expo-dev-launcher] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:33:13-67
140        <activity
140-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-21:20
141            android:name="expo.modules.devmenu.DevMenuActivity"
141-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-64
142            android:exported="true"
142-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-36
143            android:launchMode="singleTask"
143-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-44
144            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
144-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-75
145            <intent-filter>
145-->[:expo-dev-menu] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-20:29
146                <action android:name="android.intent.action.VIEW" />
146-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:7-58
146-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:17:15-56
147
148                <category android:name="android.intent.category.DEFAULT" />
148-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:9-67
148-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:34:19-65
149                <category android:name="android.intent.category.BROWSABLE" />
149-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:7-67
149-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:18:17-65
150
151                <data android:scheme="expo-dev-menu" />
151-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:7-37
151-->/Users/<USER>/Documents/dev_workshop/smartdm/android/app/src/main/AndroidManifest.xml:19:13-35
152            </intent-filter>
153        </activity>
154
155        <provider
155-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:21:9-30:20
156            android:name="expo.modules.filesystem.FileSystemFileProvider"
156-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-74
157            android:authorities="com.example.smartdm.FileSystemFileProvider"
157-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-74
158            android:exported="false"
158-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-37
159            android:grantUriPermissions="true" >
159-->[:expo-file-system] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-file-system/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:25:13-47
160            <meta-data
160-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-15:63
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:17-67
162                android:resource="@xml/file_system_provider_paths" />
162-->[:react-native-webview] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/react-native-webview/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-60
163        </provider>
164
165        <service
165-->[:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-15:56
166            android:name="expo.modules.location.services.LocationTaskService"
166-->[:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-78
167            android:exported="false"
167-->[:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-37
168            android:foregroundServiceType="location" />
168-->[:expo-location] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-location/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-53
169
170        <meta-data
170-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-11:89
171            android:name="org.unimodules.core.AppLoader#react-native-headless"
171-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-79
172            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
172-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-86
173        <meta-data
173-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-15:45
174            android:name="com.facebook.soloader.enabled"
174-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-57
175            android:value="true" />
175-->[:expo-modules-core] /Users/<USER>/Documents/dev_workshop/smartdm/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-33
176
177        <activity
177-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
178            android:name="com.google.android.gms.common.api.GoogleApiActivity"
178-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
179            android:exported="false"
179-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
180            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
180-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
181
182        <meta-data
182-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:21:9-23:69
183            android:name="com.google.android.gms.version"
183-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:22:13-58
184            android:value="@integer/google_play_services_version" />
184-->[com.google.android.gms:play-services-basement:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/AndroidManifest.xml:23:13-66
185
186        <provider
186-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:26:9-34:20
187            android:name="androidx.startup.InitializationProvider"
187-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:27:13-67
188            android:authorities="com.example.smartdm.androidx-startup"
188-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:28:13-68
189            android:exported="false" >
189-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:29:13-37
190            <meta-data
190-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:31:13-33:52
191                android:name="androidx.emoji2.text.EmojiCompatInitializer"
191-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:32:17-75
192                android:value="androidx.startup" />
192-->[androidx.emoji2:emoji2:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/eefd0b7519c726bc3ece83913f4c4101/transformed/jetified-emoji2-1.0.0/AndroidManifest.xml:33:17-49
193            <meta-data
193-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
194                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
194-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
195                android:value="androidx.startup" />
195-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/transforms-3/45ed712525aa6d6e0b742d7cbd4ed928/transformed/jetified-lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
196            <meta-data
196-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
197                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
197-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
198                android:value="androidx.startup" />
198-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
199        </provider>
200
201        <receiver
201-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
202            android:name="androidx.profileinstaller.ProfileInstallReceiver"
202-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
203            android:directBootAware="false"
203-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
204            android:enabled="true"
204-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
205            android:exported="true"
205-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
206            android:permission="android.permission.DUMP" >
206-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
208                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
208-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
211                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
211-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
211-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
214                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
214-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
217                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
217-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/transforms-3/aad97c6553544173ea27099e3e755f38/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
218            </intent-filter>
219        </receiver>
220
221        <service
221-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:19:9-21:40
222            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
222-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:20:13-132
223            android:exported="false" />
223-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:21:13-37
224        <service
224-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:22:9-24:40
225            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
225-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:23:13-127
226            android:exported="false" />
226-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:24:13-37
227        <service
227-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:25:9-27:40
228            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
228-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:26:13-120
229            android:exported="false" />
229-->[io.nlopez.smartlocation:library:3.3.3] /Users/<USER>/.gradle/caches/transforms-3/e56a6cc025369adc140f3ee078c5666b/transformed/jetified-library-3.3.3/AndroidManifest.xml:27:13-37
230    </application>
231
232</manifest>
