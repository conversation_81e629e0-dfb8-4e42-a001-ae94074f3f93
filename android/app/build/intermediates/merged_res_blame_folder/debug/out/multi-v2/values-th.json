{"logs": [{"outputFile": "com.example.smartdm.app-mergeDebugResources-49:/values-th/values-th.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,96", "endOffsets": "153,252,363,460"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "4858,4961,5060,5171", "endColumns": "102,98,110,96", "endOffsets": "4956,5055,5166,5263"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/res/values-th/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2734,2840,2987,3110,3217,3353,3477,3596,3833,3977,4082,4229,4351,4491,4642,4706,4774", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "2835,2982,3105,3212,3348,3472,3591,3699,3972,4077,4224,4346,4486,4637,4701,4769,4853"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/res/values-th/values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5350", "endColumns": "100", "endOffsets": "5446"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,5268", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,5345"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/res/values-th/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3704", "endColumns": "128", "endOffsets": "3828"}}]}]}