{"logs": [{"outputFile": "com.example.smartdm.app-mergeDebugResources-49:/values-fr/values-fr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,103", "endOffsets": "157,259,378,482"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "5249,5356,5458,5577", "endColumns": "106,101,118,103", "endOffsets": "5351,5453,5572,5676"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/res/values-fr/values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,5681", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,5763"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/res/values-fr/values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5768", "endColumns": "100", "endOffsets": "5864"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/res/values-fr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3910", "endColumns": "164", "endOffsets": "4070"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/res/values-fr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2847,2953,3133,3263,3372,3543,3676,3797,4075,4253,4365,4550,4686,4846,5025,5098,5165", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "2948,3128,3258,3367,3538,3671,3792,3905,4248,4360,4545,4681,4841,5020,5093,5160,5244"}}]}]}