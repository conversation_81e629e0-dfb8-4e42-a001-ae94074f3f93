{"logs": [{"outputFile": "com.example.smartdm.app-mergeDebugResources-49:/values-uz/values-uz.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,5459", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,5538"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/res/values-uz/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2776,2881,3031,3160,3269,3414,3547,3667,3923,4095,4203,4362,4494,4648,4810,4876,4937", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "2876,3026,3155,3264,3409,3542,3662,3768,4090,4198,4357,4489,4643,4805,4871,4932,5012"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/res/values-uz/values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5543", "endColumns": "100", "endOffsets": "5639"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,104", "endOffsets": "168,277,387,492"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "5017,5135,5244,5354", "endColumns": "117,108,109,104", "endOffsets": "5130,5239,5349,5454"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/res/values-uz/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3773", "endColumns": "149", "endOffsets": "3918"}}]}]}