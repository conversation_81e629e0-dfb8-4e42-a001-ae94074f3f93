{"logs": [{"outputFile": "com.example.smartdm.app-mergeDebugResources-49:/values-h480dp-land-v13/values-h480dp-land-v13.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/26ee83666d100809ff6549bb4a930a0e/transformed/material-1.2.1/res/values-h480dp-land-v13/values-h480dp-land-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,170,226,293,358,413,478,547", "endColumns": "58,55,55,66,64,54,64,68,68", "endOffsets": "109,165,221,288,353,408,473,542,611"}}]}]}