{"logs": [{"outputFile": "com.example.smartdm.app-mergeDebugResources-49:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/c69337d656e1633e348f75128bb75e8e/transformed/jetified-play-services-base-18.0.1/res/values-zh-rHK/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2666,2767,2895,3010,3112,3219,3335,3437,3638,3748,3849,3978,4093,4200,4308,4363,4420", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "2762,2890,3005,3107,3214,3330,3432,3525,3743,3844,3973,4088,4195,4303,4358,4415,4487"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/e2143c8b17d71a22a2f2340e95988c97/transformed/jetified-play-services-basement-18.0.0/res/values-zh-rHK/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3530", "endColumns": "107", "endOffsets": "3633"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5ea3ee2f6797157a0c0d988e92b854a2/transformed/browser-1.2.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,91", "endOffsets": "133,225,326,418"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "4492,4575,4667,4768", "endColumns": "82,91,100,91", "endOffsets": "4570,4662,4763,4855"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/83271a3b68deb930daa95769f879a316/transformed/appcompat-1.4.1/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,4860", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,4934"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/d1981ab41a36cb2b1aaf83dd5679fd13/transformed/core-1.8.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "4939", "endColumns": "100", "endOffsets": "5035"}}]}]}