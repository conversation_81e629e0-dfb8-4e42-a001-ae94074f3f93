import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Chrome as Home, Scan, Cpu, Settings } from 'lucide-react-native';

// Import your existing screens
import HomeScreen from './app/(tabs)/index';
import ScannerScreen from './app/(tabs)/scanner';
import MonitorScreen from './app/(tabs)/monitor';
import SettingsScreen from './app/(tabs)/settings';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: '#8E8E93',
          headerShown: false,
        }}
      >
        <Tab.Screen
          name="Home"
          component={HomeScreen}
          options={{
            title: '首页',
            tabBarIcon: ({ color, size }) => <Home size={size} color={color} />,
          }}
        />
        <Tab.Screen
          name="Scanner"
          component={ScannerScreen}
          options={{
            title: '扫码',
            tabBarIcon: ({ color, size }) => <Scan size={size} color={color} />,
          }}
        />
        <Tab.Screen
          name="Monitor"
          component={MonitorScreen}
          options={{
            title: '监控',
            tabBarIcon: ({ color, size }) => <Cpu size={size} color={color} />,
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: '设置',
            tabBarIcon: ({ color, size }) => <Settings size={size} color={color} />,
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}
