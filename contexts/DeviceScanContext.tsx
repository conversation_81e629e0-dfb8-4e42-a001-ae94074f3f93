import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import AndroidNetworkTools, { DeviceInfo, NetworkScanResult } from '../modules/android-network-tools';

export interface ScanProgress {
  phase: 'idle' | 'scanning' | 'analyzing' | 'completed' | 'error';
  progress: number; // 0-100
  devicesFound: number;
  message: string;
  currentDevice?: string;
}

export interface ScannedDevice extends DeviceInfo {
  id: string;
  deviceType: string;
  status: 'online' | 'offline' | 'unknown';
  lastSeen: Date;
  isReachable?: boolean;
  responseTime?: number;
}

interface DeviceScanContextType {
  // 扫描状态
  isScanning: boolean;
  scanProgress: ScanProgress;
  error: string | null;
  
  // 设备数据
  devices: ScannedDevice[];
  lastScanTime: Date | null;
  
  // 扫描操作
  startNetworkScan: () => Promise<void>;
  stopScan: () => void;
  clearDevices: () => void;
  refreshDevice: (deviceId: string) => Promise<void>;
}

const DeviceScanContext = createContext<DeviceScanContextType | undefined>(undefined);

interface DeviceScanProviderProps {
  children: ReactNode;
}

export function DeviceScanProvider({ children }: DeviceScanProviderProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState<ScanProgress>({
    phase: 'idle',
    progress: 0,
    devicesFound: 0,
    message: '准备扫描网络设备'
  });
  const [error, setError] = useState<string | null>(null);
  const [devices, setDevices] = useState<ScannedDevice[]>([]);
  const [lastScanTime, setLastScanTime] = useState<Date | null>(null);

  // 推断设备类型
  const inferDeviceType = (device: DeviceInfo): string => {
    const hostname = (device.hostname || '').toLowerCase();
    const ip = device.ip || '';
    
    // 基于hostname推断
    if (hostname.includes('router') || hostname.includes('gateway')) {
      return 'router';
    } else if (hostname.includes('printer') || hostname.includes('hp') || hostname.includes('canon')) {
      return 'printer';
    } else if (hostname.includes('camera') || hostname.includes('cam')) {
      return 'camera';
    } else if (hostname.includes('tv') || hostname.includes('smart')) {
      return 'smart-tv';
    } else if (hostname.includes('phone') || hostname.includes('android') || hostname.includes('iphone')) {
      return 'mobile';
    } else if (hostname.includes('pc') || hostname.includes('desktop') || hostname.includes('laptop')) {
      return 'computer';
    }
    
    // 基于IP地址推断（网关通常是.1）
    if (ip.endsWith('.1')) {
      return 'router';
    }
    
    return 'unknown';
  };

  // 开始网络扫描
  const startNetworkScan = useCallback(async () => {
    console.log('🔥 startNetworkScan 函数被调用了！');
    console.log('📊 当前扫描状态:', { isScanning, error, devicesCount: devices.length });
    
    if (isScanning) {
      console.log('⚠️ 扫描已在进行中，跳过重复扫描');
      return;
    }

    console.log('🚀 设置扫描状态为 true...');
    setIsScanning(true);
    setError(null);
    
    try {
      console.log('🔍 开始网络设备扫描...');
      
      // 阶段1: 初始化
      setScanProgress({
        phase: 'scanning',
        progress: 10,
        devicesFound: 0,
        message: '正在扫描局域网设备...'
      });

      // 检查AndroidNetworkTools可用性
      console.log('🔍 检查 AndroidNetworkTools 可用性...');
      console.log('📦 AndroidNetworkTools 对象:', AndroidNetworkTools);
      
      const isAvailable = await AndroidNetworkTools.isAvailable();
      console.log('📊 AndroidNetworkTools.isAvailable() 结果:', isAvailable);
      
      if (!isAvailable) {
        console.error('❌ AndroidNetworkTools模块不可用');
        throw new Error('AndroidNetworkTools模块不可用');
      }

      // 阶段2: ARP扫描
      console.log('🔍 开始 ARP 扫描阶段...');
      setScanProgress({
        phase: 'scanning',
        progress: 30,
        devicesFound: 0,
        message: '正在进行ARP扫描...'
      });

      console.log('🚀 调用 AndroidNetworkTools.performFullNetworkScan()...');
      const scanResult: NetworkScanResult = await AndroidNetworkTools.performFullNetworkScan();
      console.log('📊 扫描结果:', scanResult);
      
      // 阶段3: 分析设备
      setScanProgress({
        phase: 'analyzing',
        progress: 70,
        devicesFound: scanResult.devices.length,
        message: `发现 ${scanResult.devices.length} 个设备，正在分析...`
      });

      // 转换为ScannedDevice格式
      const scannedDevices: ScannedDevice[] = scanResult.devices.map((device, index) => ({
        ...device,
        id: `device-${device.ip.replace(/\./g, '-')}`,
        deviceType: inferDeviceType(device),
        status: 'online' as const,
        lastSeen: new Date(),
      }));

      // 阶段4: 完成
      setScanProgress({
        phase: 'completed',
        progress: 100,
        devicesFound: scannedDevices.length,
        message: `扫描完成！发现 ${scannedDevices.length} 个设备`
      });

      setDevices(scannedDevices);
      setLastScanTime(new Date());
      
      console.log(`✅ 网络扫描完成，发现 ${scannedDevices.length} 个设备`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '扫描失败';
      console.error('💥 网络扫描失败:', err);
      
      setError(errorMessage);
      setScanProgress({
        phase: 'error',
        progress: 0,
        devicesFound: 0,
        message: `扫描失败: ${errorMessage}`
      });
    } finally {
      setIsScanning(false);
      
      // 3秒后重置进度状态
      setTimeout(() => {
        setScanProgress(prev => ({
          ...prev,
          phase: 'idle',
          message: prev.phase === 'completed' ? `上次扫描: ${prev.devicesFound} 个设备` : '准备扫描网络设备'
        }));
      }, 3000);
    }
  }, [isScanning]);

  // 停止扫描
  const stopScan = useCallback(() => {
    setIsScanning(false);
    setScanProgress({
      phase: 'idle',
      progress: 0,
      devicesFound: devices.length,
      message: '扫描已停止'
    });
  }, [devices.length]);

  // 清除设备列表
  const clearDevices = useCallback(() => {
    setDevices([]);
    setLastScanTime(null);
    setScanProgress({
      phase: 'idle',
      progress: 0,
      devicesFound: 0,
      message: '准备扫描网络设备'
    });
  }, []);

  // 刷新单个设备
  const refreshDevice = useCallback(async (deviceId: string) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    try {
      const pingResult = await AndroidNetworkTools.pingDevice(device.ip);
      
      setDevices(prev => prev.map(d => 
        d.id === deviceId 
          ? {
              ...d,
              status: pingResult.isReachable ? 'online' : 'offline',
              isReachable: pingResult.isReachable,
              responseTime: pingResult.responseTime,
              lastSeen: new Date()
            }
          : d
      ));
    } catch (error) {
      console.error(`Failed to refresh device ${deviceId}:`, error);
    }
  }, [devices]);

  const value: DeviceScanContextType = {
    isScanning,
    scanProgress,
    error,
    devices,
    lastScanTime,
    startNetworkScan,
    stopScan,
    clearDevices,
    refreshDevice,
  };

  return (
    <DeviceScanContext.Provider value={value}>
      {children}
    </DeviceScanContext.Provider>
  );
}

export function useDeviceScan() {
  const context = useContext(DeviceScanContext);
  if (context === undefined) {
    throw new Error('useDeviceScan must be used within a DeviceScanProvider');
  }
  return context;
}
