-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml:2:13-83
INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml:2:13-83
	package
		INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml:2:23-81
uses-sdk
INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml
INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/dev_workshop/android-network-tools/library/build/intermediates/tmp/ProcessLibraryManifest/debug/tempAndroidManifest5142634380612742381.xml
