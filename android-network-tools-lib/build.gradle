plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    namespace "com.stealthcotper.networktools"
    compileSdkVersion compileSdkVer
    buildToolsVersion buildToolsVer

    defaultConfig {
        minSdkVersion minSdkVer
        targetSdkVersion targetSdkVer

        versionName appVersionName
        versionCode appVersionCode

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation "androidx.core:core-ktx:$coreKtxVer"
    implementation "androidx.appcompat:appcompat:$appcompatVer"
    implementation "com.google.android.material:material:$materialVer"

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}