const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 减少文件监控，避免EMFILE错误
config.watchFolders = [];

// 忽略不必要的文件和目录
config.resolver.blockList = [
  /android\/build\/.*/,
  /android\/\.gradle\/.*/,
  /ios\/build\/.*/,
  /ios\/Pods\/.*/,
  /node_modules\/.*\/android\/build\/.*/,
  /node_modules\/.*\/ios\/build\/.*/,
  /\.expo\/.*/,
  /dist\/.*/,
  /build\/.*/,
  /coverage\/.*/,
  /tmp\/.*/,
  /temp\/.*/
];

// 减少文件系统监控 - 简化版本
config.server = {
  ...config.server,
};

module.exports = config;
