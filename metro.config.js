const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// 减少文件监控，避免EMFILE错误
config.watchFolders = [];

// 忽略不必要的文件和目录
config.resolver.blockList = [
  /android\/build\/.*/,
  /android\/\.gradle\/.*/,
  /ios\/build\/.*/,
  /ios\/Pods\/.*/,
  /node_modules\/.*\/android\/build\/.*/,
  /node_modules\/.*\/ios\/build\/.*/,
  /\.expo\/.*/,
  /dist\/.*/,
  /build\/.*/,
  /coverage\/.*/,
  /tmp\/.*/,
  /temp\/.*/
];

// 减少文件系统监控
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // 减少不必要的文件访问
      if (req.url.includes('/node_modules/') || 
          req.url.includes('/android/build/') ||
          req.url.includes('/.expo/') ||
          req.url.includes('/build/')) {
        res.status(404).end();
        return;
      }
      return middleware(req, res, next);
    };
  },
};

module.exports = config;
