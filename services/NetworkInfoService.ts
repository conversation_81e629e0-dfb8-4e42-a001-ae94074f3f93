import { Platform } from 'react-native';
import * as Network from 'expo-network';

export interface NetworkInfo {
  isConnected: boolean;
  ssid: string | null;
  ipAddress: string | null;
  subnet: string | null;
  gateway: string | null;
  dns: string[] | null;
  type: string | null;
}

/**
 * 检查并请求必要的网络权限
 */
export async function checkAndRequestPermissions(): Promise<boolean> {
  try {
    // 在Expo环境中，网络权限通常是自动授予的
    // 这里主要是为了兼容性检查
    console.log('📋 检查网络权限...');
    
    if (Platform.OS === 'android') {
      // Android权限检查
      console.log('✅ Android网络权限已授予');
      return true;
    } else if (Platform.OS === 'ios') {
      // iOS权限检查
      console.log('✅ iOS网络权限已授予');
      return true;
    } else {
      // Web平台
      console.log('✅ Web平台网络权限已授予');
      return true;
    }
  } catch (error) {
    console.error('💥 权限检查失败:', error);
    return false;
  }
}

/**
 * 获取增强的网络信息
 */
export async function getEnhancedNetworkInfo(): Promise<NetworkInfo> {
  try {
    console.log('🔍 获取网络状态...');
    
    // 获取网络状态
    const networkState = await Network.getNetworkStateAsync();
    console.log('📊 网络状态:', networkState);
    
    // 获取IP地址
    const ipAddress = await Network.getIpAddressAsync();
    console.log('📊 IP地址:', ipAddress);
    
    // 构建网络信息对象
    const networkInfo: NetworkInfo = {
      isConnected: networkState.isConnected || false,
      ssid: null,
      ipAddress: ipAddress || null,
      subnet: null,
      gateway: null,
      dns: null,
      type: networkState.type || null,
    };

    // 如果是WiFi连接，尝试获取更多信息
    if (networkState.type === Network.NetworkStateType.WIFI) {
      try {
        // 在真实设备上，可以通过原生模块获取WiFi信息
        // 这里使用模拟数据
        networkInfo.ssid = 'WiFi-Network';
        
        if (ipAddress) {
          // 从IP地址推断子网信息
          const ipParts = ipAddress.split('.');
          if (ipParts.length === 4) {
            networkInfo.subnet = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.0/24`;
            networkInfo.gateway = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.1`;
          }
        }
      } catch (wifiError) {
        console.warn('⚠️ 获取WiFi详细信息失败:', wifiError);
      }
    }

    console.log('✅ 网络信息获取完成:', networkInfo);
    return networkInfo;
    
  } catch (error) {
    console.error('💥 获取网络信息失败:', error);
    
    // 返回默认的网络信息
    return {
      isConnected: false,
      ssid: null,
      ipAddress: null,
      subnet: null,
      gateway: null,
      dns: null,
      type: null,
    };
  }
}

/**
 * 从IP地址和子网掩码计算子网地址
 */
export function getSubnetFromIP(ipAddress: string, subnetMask?: string): string {
  try {
    if (!ipAddress) {
      return '192.168.1';
    }

    const ipParts = ipAddress.split('.');
    if (ipParts.length !== 4) {
      return '192.168.1';
    }

    // 简单的子网计算，假设是/24网络
    return `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}`;
    
  } catch (error) {
    console.error('💥 子网计算失败:', error);
    return '192.168.1';
  }
}

/**
 * 检查IP地址是否在同一子网
 */
export function isInSameSubnet(ip1: string, ip2: string, subnetMask: string = '*************'): boolean {
  try {
    const ip1Parts = ip1.split('.').map(Number);
    const ip2Parts = ip2.split('.').map(Number);
    const maskParts = subnetMask.split('.').map(Number);

    for (let i = 0; i < 4; i++) {
      if ((ip1Parts[i] & maskParts[i]) !== (ip2Parts[i] & maskParts[i])) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('💥 子网比较失败:', error);
    return false;
  }
}

/**
 * 验证IP地址格式
 */
export function isValidIPAddress(ip: string): boolean {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
}

/**
 * 生成IP地址范围
 */
export function generateIPRange(subnet: string, startIP: number = 1, endIP: number = 254): string[] {
  const ips: string[] = [];
  
  for (let i = startIP; i <= endIP; i++) {
    ips.push(`${subnet}.${i}`);
  }
  
  return ips;
}

/**
 * 获取本地网络接口信息
 */
export async function getNetworkInterfaces(): Promise<any[]> {
  try {
    // 在移动端，这个功能有限
    // 主要返回当前连接的网络信息
    const networkInfo = await getEnhancedNetworkInfo();
    
    return [{
      name: 'wlan0',
      address: networkInfo.ipAddress,
      netmask: '*************',
      family: 'IPv4',
      mac: '00:00:00:00:00:00', // 移动端通常无法获取MAC地址
      internal: false
    }];
    
  } catch (error) {
    console.error('💥 获取网络接口失败:', error);
    return [];
  }
}
