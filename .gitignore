# dependencies
node_modules/

# expo
.expo/
dist/
web-build/
expo-env.d.ts

# native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macos
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# android
android/build/
android/.gradle/
android/app/build/
android/gradle/
*.apk
*.aab

# ios
ios/build/
ios/Pods/
ios/DerivedData/
*.ipa

# build artifacts
build/
dist/
coverage/
.nyc_output/
tmp/
temp/

# cache
.cache/
*.log
