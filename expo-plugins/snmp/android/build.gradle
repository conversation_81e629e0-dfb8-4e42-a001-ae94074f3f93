apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.example.smartdm.snmp'
    compileSdk 35

    defaultConfig {
        minSdk 24
        targetSdk 35
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

repositories {
    google()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    // 使用一个简单的 SNMP 库进行测试
    implementation 'org.snmp4j:snmp4j:3.7.7'
}