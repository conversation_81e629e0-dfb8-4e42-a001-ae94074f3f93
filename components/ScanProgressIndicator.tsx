import React from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Search, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react-native';
import { ScanProgress } from '@/contexts/DeviceScanContext';

interface ScanProgressIndicatorProps {
  progress: ScanProgress;
  visible: boolean;
}

export default function ScanProgressIndicator({ progress, visible }: ScanProgressIndicatorProps) {
  const progressAnim = React.useRef(new Animated.Value(0)).current;
  const opacityAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    // 更新进度条动画
    Animated.timing(progressAnim, {
      toValue: progress.progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress.progress]);

  React.useEffect(() => {
    // 控制显示/隐藏动画
    Animated.timing(opacityAnim, {
      toValue: visible ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [visible]);

  const getPhaseIcon = () => {
    switch (progress.phase) {
      case 'scanning':
        return <RefreshCw size={16} color="#1890ff" />;
      case 'analyzing':
        return <Search size={16} color="#faad14" />;
      case 'completed':
        return <CheckCircle size={16} color="#52c41a" />;
      case 'error':
        return <AlertCircle size={16} color="#ff4d4f" />;
      default:
        return <Search size={16} color="#d9d9d9" />;
    }
  };

  const getPhaseColor = () => {
    switch (progress.phase) {
      case 'scanning':
        return '#1890ff';
      case 'analyzing':
        return '#faad14';
      case 'completed':
        return '#52c41a';
      case 'error':
        return '#ff4d4f';
      default:
        return '#d9d9d9';
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, { opacity: opacityAnim }]}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          {getPhaseIcon()}
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.message}>{progress.message}</Text>
          {progress.devicesFound > 0 && (
            <Text style={styles.deviceCount}>
              发现 {progress.devicesFound} 个设备
            </Text>
          )}
        </View>
        <Text style={styles.percentage}>{progress.progress}%</Text>
      </View>
      
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground}>
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                }),
                backgroundColor: getPhaseColor(),
              },
            ]}
          />
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  message: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  deviceCount: {
    fontSize: 12,
    color: '#666',
  },
  percentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1890ff',
    marginLeft: 8,
  },
  progressBarContainer: {
    marginTop: 4,
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#f0f0f0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 2,
  },
});
