{"name": "bolt-expo-starter", "main": "node_modules/expo-router/entry.js", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "^7.27.6", "@expo/metro-runtime": "^5.0.4", "@expo/vector-icons": "^13.0.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "expo": "^52.0.0", "expo-application": "~5.3.0", "expo-blur": "~12.4.1", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-dev-client": "~2.4.12", "expo-font": "~11.4.0", "expo-haptics": "~12.4.0", "expo-linear-gradient": "~12.3.0", "expo-linking": "~5.0.2", "expo-location": "~16.1.0", "expo-router": "^2.0.0", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~2.4.0", "expo-web-browser": "~12.3.2", "lucide-react-native": "^0.475.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.75.5", "react-native-gesture-handler": "~2.24.0", "react-native-network-info": "^5.2.1", "react-native-safe-area-context": "^4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.19.6", "react-native-webview": "13.2.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native/gradle-plugin": "0.75.5", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}