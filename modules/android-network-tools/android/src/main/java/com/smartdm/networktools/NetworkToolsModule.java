package com.smartdm.networktools;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

import com.stealthcopter.networktools.SubnetDevices;
import com.stealthcopter.networktools.Ping;
import com.stealthcopter.networktools.PortScan;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import android.util.Log;

public class NetworkToolsModule extends ReactContextBaseJavaModule {
    
    private static final String MODULE_NAME = "AndroidNetworkTools";
    private static final String TAG = "NetworkTools";
    private ExecutorService executor = Executors.newCachedThreadPool();
    
    public NetworkToolsModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return MODULE_NAME;
    }
    
    /**
     * 测试模块是否可用
     */
    @ReactMethod
    public void isAvailable(Promise promise) {
        try {
            Log.d(TAG, "NetworkTools module is available");
            promise.resolve(true);
        } catch (Exception e) {
            Log.e(TAG, "NetworkTools module test failed", e);
            promise.reject("MODULE_ERROR", "NetworkTools module not available: " + e.getMessage());
        }
    }
    
    /**
     * ARP扫描发现局域网设备
     */
    @ReactMethod
    public void scanSubnetDevices(Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Starting ARP subnet scan...");
                WritableArray devices = Arguments.createArray();
                
                SubnetDevices.fromLocalAddress().findDevices(new SubnetDevices.OnSubnetDeviceFound() {
                    @Override
                    public void onDeviceFound(SubnetDevices.Device device) {
                        Log.d(TAG, "Found device: " + device.ip + " - " + device.hostname);
                        
                        WritableMap deviceInfo = Arguments.createMap();
                        deviceInfo.putString("ip", device.ip);
                        deviceInfo.putString("hostname", device.hostname != null ? device.hostname : "Unknown");
                        deviceInfo.putString("mac", device.mac != null ? device.mac : "Unknown");
                        deviceInfo.putDouble("responseTime", device.time);
                        deviceInfo.putString("discoveryMethod", "ARP");
                        
                        // 获取设备厂商信息
                        String vendor = getMacVendor(device.mac);
                        deviceInfo.putString("vendor", vendor);
                        
                        // 推断设备类型
                        String deviceType = inferDeviceType(device.hostname, device.mac, vendor);
                        deviceInfo.putString("type", deviceType);
                        
                        devices.pushMap(deviceInfo);
                    }
                    
                    @Override
                    public void onFinished(ArrayList<SubnetDevices.Device> devicesFound) {
                        Log.d(TAG, "ARP scan completed. Found " + devicesFound.size() + " devices");
                        promise.resolve(devices);
                    }
                });
                
            } catch (Exception e) {
                Log.e(TAG, "ARP scan failed", e);
                promise.reject("ARP_SCAN_ERROR", "ARP扫描失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * Ping测试单个设备
     */
    @ReactMethod
    public void pingDevice(String ipAddress, Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Pinging device: " + ipAddress);
                
                Ping.onAddress(ipAddress)
                    .setTimeOutMillis(3000)
                    .setTimes(1)
                    .doPing(new Ping.PingListener() {
                        @Override
                        public void onResult(Ping.PingResult pingResult) {
                            WritableMap result = Arguments.createMap();
                            result.putString("ip", ipAddress);
                            result.putBoolean("isReachable", pingResult.isReachable);
                            result.putDouble("responseTime", pingResult.timeTaken);
                            result.putString("error", pingResult.error);
                            
                            Log.d(TAG, "Ping result for " + ipAddress + ": " + pingResult.isReachable);
                            promise.resolve(result);
                        }
                        
                        @Override
                        public void onError(Exception e) {
                            Log.e(TAG, "Ping error for " + ipAddress, e);
                            promise.reject("PING_ERROR", "Ping失败: " + e.getMessage());
                        }
                    });
                    
            } catch (Exception e) {
                Log.e(TAG, "Ping setup failed for " + ipAddress, e);
                promise.reject("PING_ERROR", "Ping设置失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 扫描设备开放端口
     */
    @ReactMethod
    public void scanDevicePorts(String ipAddress, Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Scanning ports for device: " + ipAddress);
                
                int[] commonPorts = {21, 22, 23, 25, 53, 80, 110, 143, 161, 443, 993, 995, 8080, 9100};
                WritableArray openPorts = Arguments.createArray();
                WritableArray services = Arguments.createArray();
                
                int completedScans = 0;
                final int totalPorts = commonPorts.length;
                
                for (int port : commonPorts) {
                    PortScan.onAddress(ipAddress)
                        .setPort(port)
                        .setTimeOutMillis(2000)
                        .doScan(new PortScan.PortListener() {
                            @Override
                            public void onResult(PortScan.PortResult portResult) {
                                synchronized (openPorts) {
                                    if (portResult.isOpen()) {
                                        Log.d(TAG, "Found open port " + port + " on " + ipAddress);
                                        openPorts.pushInt(port);
                                        
                                        // 识别服务类型
                                        String service = identifyService(port);
                                        if (service != null) {
                                            WritableMap serviceInfo = Arguments.createMap();
                                            serviceInfo.putInt("port", port);
                                            serviceInfo.putString("service", service);
                                            services.pushMap(serviceInfo);
                                        }
                                    }
                                    
                                    // 检查是否所有端口都扫描完成
                                    if (++completedScans >= totalPorts) {
                                        WritableMap result = Arguments.createMap();
                                        result.putString("ip", ipAddress);
                                        result.putArray("openPorts", openPorts);
                                        result.putArray("services", services);
                                        
                                        Log.d(TAG, "Port scan completed for " + ipAddress + 
                                              ". Found " + openPorts.size() + " open ports");
                                        promise.resolve(result);
                                    }
                                }
                            }
                            
                            @Override
                            public void onFinished() {
                                // 单个端口扫描完成
                            }
                        });
                }
                
                // 设置超时，防止永远等待
                executor.schedule(() -> {
                    if (completedScans < totalPorts) {
                        Log.w(TAG, "Port scan timeout for " + ipAddress);
                        WritableMap result = Arguments.createMap();
                        result.putString("ip", ipAddress);
                        result.putArray("openPorts", openPorts);
                        result.putArray("services", services);
                        result.putString("warning", "扫描超时，结果可能不完整");
                        promise.resolve(result);
                    }
                }, 15, TimeUnit.SECONDS);
                
            } catch (Exception e) {
                Log.e(TAG, "Port scan failed for " + ipAddress, e);
                promise.reject("PORT_SCAN_ERROR", "端口扫描失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 获取增强的设备信息
     */
    @ReactMethod
    public void getEnhancedDeviceInfo(String ipAddress, Promise promise) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Getting enhanced info for device: " + ipAddress);
                
                WritableMap deviceInfo = Arguments.createMap();
                deviceInfo.putString("ip", ipAddress);
                deviceInfo.putString("scanTime", String.valueOf(System.currentTimeMillis()));
                
                // 1. Ping测试
                Ping.onAddress(ipAddress)
                    .setTimeOutMillis(3000)
                    .setTimes(1)
                    .doPing(new Ping.PingListener() {
                        @Override
                        public void onResult(Ping.PingResult pingResult) {
                            deviceInfo.putBoolean("isReachable", pingResult.isReachable);
                            deviceInfo.putDouble("responseTime", pingResult.timeTaken);
                            
                            if (pingResult.isReachable) {
                                // 2. 端口扫描
                                scanPortsForEnhancedInfo(ipAddress, deviceInfo, promise);
                            } else {
                                Log.d(TAG, "Device " + ipAddress + " is not reachable");
                                promise.resolve(deviceInfo);
                            }
                        }
                        
                        @Override
                        public void onError(Exception e) {
                            Log.e(TAG, "Enhanced info ping failed for " + ipAddress, e);
                            deviceInfo.putBoolean("isReachable", false);
                            deviceInfo.putString("error", e.getMessage());
                            promise.resolve(deviceInfo);
                        }
                    });
                    
            } catch (Exception e) {
                Log.e(TAG, "Enhanced device info failed for " + ipAddress, e);
                promise.reject("ENHANCED_INFO_ERROR", "获取增强设备信息失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 为增强信息扫描端口
     */
    private void scanPortsForEnhancedInfo(String ipAddress, WritableMap deviceInfo, Promise promise) {
        int[] quickPorts = {22, 80, 161, 443, 8080}; // 快速扫描关键端口
        WritableArray openPorts = Arguments.createArray();
        WritableArray services = Arguments.createArray();
        
        int completedScans = 0;
        final int totalPorts = quickPorts.length;
        
        for (int port : quickPorts) {
            PortScan.onAddress(ipAddress)
                .setPort(port)
                .setTimeOutMillis(1500)
                .doScan(new PortScan.PortListener() {
                    @Override
                    public void onResult(PortScan.PortResult portResult) {
                        synchronized (openPorts) {
                            if (portResult.isOpen()) {
                                openPorts.pushInt(port);
                                
                                String service = identifyService(port);
                                if (service != null) {
                                    WritableMap serviceInfo = Arguments.createMap();
                                    serviceInfo.putInt("port", port);
                                    serviceInfo.putString("service", service);
                                    services.pushMap(serviceInfo);
                                }
                            }
                            
                            if (++completedScans >= totalPorts) {
                                deviceInfo.putArray("openPorts", openPorts);
                                deviceInfo.putArray("services", services);
                                
                                // 检测SNMP支持
                                boolean snmpSupported = false;
                                for (int i = 0; i < openPorts.size(); i++) {
                                    if (openPorts.getInt(i) == 161) {
                                        snmpSupported = true;
                                        break;
                                    }
                                }
                                deviceInfo.putBoolean("snmpSupported", snmpSupported);
                                
                                // 推断设备类型
                                String detectedType = analyzeDeviceType(services);
                                deviceInfo.putString("detectedType", detectedType);
                                
                                Log.d(TAG, "Enhanced info completed for " + ipAddress);
                                promise.resolve(deviceInfo);
                            }
                        }
                    }
                    
                    @Override
                    public void onFinished() {
                        // 单个端口扫描完成
                    }
                });
        }
        
        // 超时保护
        executor.schedule(() -> {
            if (completedScans < totalPorts) {
                deviceInfo.putArray("openPorts", openPorts);
                deviceInfo.putArray("services", services);
                deviceInfo.putString("warning", "端口扫描超时");
                promise.resolve(deviceInfo);
            }
        }, 8, TimeUnit.SECONDS);
    }
    
    /**
     * 获取MAC地址厂商信息
     */
    private String getMacVendor(String macAddress) {
        if (macAddress == null || macAddress.length() < 8 || "Unknown".equals(macAddress)) {
            return "Unknown";
        }
        
        try {
            // 提取OUI (前6位)
            String oui = macAddress.substring(0, 8).toUpperCase().replace(":", "");
            
            // 常见厂商OUI映射
            switch (oui) {
                case "001A2B": case "00259C": case "0026CA": return "Cisco";
                case "001B78": case "002264": case "0025B3": return "HP";
                case "001C23": case "002219": case "0025B5": return "Dell";
                case "001E10": case "002713": case "0025FE": return "Huawei";
                case "001F5B": case "002332": case "0026BB": return "Apple";
                case "001D25": case "002454": case "0025D3": return "Samsung";
                case "001EC9": case "002618": case "0027E3": return "Xiaomi";
                case "001B63": case "002243": case "0025BC": return "TP-Link";
                default: return "Unknown";
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to parse MAC vendor: " + macAddress, e);
            return "Unknown";
        }
    }
    
    /**
     * 推断设备类型
     */
    private String inferDeviceType(String hostname, String mac, String vendor) {
        if (hostname != null && !hostname.equals("Unknown")) {
            String lowerHostname = hostname.toLowerCase();
            
            if (lowerHostname.contains("router") || lowerHostname.contains("gateway")) {
                return "router";
            } else if (lowerHostname.contains("switch")) {
                return "switch";
            } else if (lowerHostname.contains("printer")) {
                return "printer";
            } else if (lowerHostname.contains("camera") || lowerHostname.contains("ipc")) {
                return "camera";
            } else if (lowerHostname.contains("nas") || lowerHostname.contains("storage")) {
                return "storage";
            } else if (lowerHostname.contains("phone") || lowerHostname.contains("mobile")) {
                return "mobile";
            } else if (lowerHostname.contains("pc") || lowerHostname.contains("desktop")) {
                return "computer";
            }
        }
        
        // 基于厂商推断
        if (vendor != null && !vendor.equals("Unknown")) {
            switch (vendor.toLowerCase()) {
                case "cisco":
                case "huawei":
                case "tp-link":
                    return "router";
                case "hp":
                    return "printer";
                case "apple":
                case "samsung":
                case "xiaomi":
                    return "mobile";
                default:
                    return "unknown";
            }
        }
        
        return "unknown";
    }
    
    /**
     * 识别端口服务
     */
    private String identifyService(int port) {
        switch (port) {
            case 21: return "FTP";
            case 22: return "SSH";
            case 23: return "Telnet";
            case 25: return "SMTP";
            case 53: return "DNS";
            case 80: return "HTTP";
            case 110: return "POP3";
            case 143: return "IMAP";
            case 161: return "SNMP";
            case 443: return "HTTPS";
            case 993: return "IMAPS";
            case 995: return "POP3S";
            case 8080: return "HTTP-Alt";
            case 9100: return "Printer";
            default: return null;
        }
    }
    
    /**
     * 基于服务分析设备类型
     */
    private String analyzeDeviceType(WritableArray services) {
        try {
            boolean hasHTTP = false;
            boolean hasHTTPS = false;
            boolean hasSNMP = false;
            boolean hasSSH = false;
            boolean hasPrinter = false;
            
            for (int i = 0; i < services.size(); i++) {
                WritableMap service = services.getMap(i);
                String serviceName = service.getString("service");
                
                if ("HTTP".equals(serviceName) || "HTTP-Alt".equals(serviceName)) {
                    hasHTTP = true;
                } else if ("HTTPS".equals(serviceName)) {
                    hasHTTPS = true;
                } else if ("SNMP".equals(serviceName)) {
                    hasSNMP = true;
                } else if ("SSH".equals(serviceName)) {
                    hasSSH = true;
                } else if ("Printer".equals(serviceName)) {
                    hasPrinter = true;
                }
            }
            
            if (hasPrinter) {
                return "printer";
            } else if (hasSNMP && (hasHTTP || hasHTTPS)) {
                return "router";
            } else if (hasSSH && (hasHTTP || hasHTTPS)) {
                return "server";
            } else if (hasHTTP || hasHTTPS) {
                return "web-device";
            } else {
                return "unknown";
            }
            
        } catch (Exception e) {
            Log.w(TAG, "Failed to analyze device type from services", e);
            return "unknown";
        }
    }
}
