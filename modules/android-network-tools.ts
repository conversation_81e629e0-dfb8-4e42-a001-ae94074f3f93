import { NativeModules } from 'react-native';

const { AndroidNetworkTools: NativeAndroidNetworkTools } = NativeModules;

export interface DeviceInfo {
  ip: string;
  hostname: string;
  mac: string;
}

export interface PingResult {
  isReachable: boolean;
  responseTime: number;
  ip: string;
}

export interface PortScanResult {
  openPorts: number[];
  services: Array<{
    port: number;
    service: string;
  }>;
  ip: string;
}

export interface EnhancedDeviceInfo {
  ip: string;
  hostname: string;
  mac: string;
  detectedType: string;
  snmpSupported: boolean;
  openPorts: number[];
  services: string[];
}

export interface NetworkScanResult {
  devices: DeviceInfo[];
  scanSummary: {
    totalDevices: number;
    snmpDevices: number;
    scanDuration: number;
  };
}

class AndroidNetworkToolsWrapper {
  async isAvailable(): Promise<boolean> {
    try {
      if (!NativeAndroidNetworkTools) {
        return false;
      }
      return await NativeAndroidNetworkTools.isAvailable();
    } catch (error) {
      console.error('AndroidNetworkTools.isAvailable error:', error);
      return false;
    }
  }

  async scanSubnetDevices(): Promise<DeviceInfo[]> {
    try {
      if (!NativeAndroidNetworkTools) {
        throw new Error('AndroidNetworkTools native module not available');
      }
      return await NativeAndroidNetworkTools.scanSubnetDevices();
    } catch (error) {
      console.error('AndroidNetworkTools.scanSubnetDevices error:', error);
      throw error;
    }
  }

  async pingDevice(ip: string): Promise<PingResult> {
    try {
      if (!NativeAndroidNetworkTools) {
        throw new Error('AndroidNetworkTools native module not available');
      }
      return await NativeAndroidNetworkTools.pingDevice(ip);
    } catch (error) {
      console.error('AndroidNetworkTools.pingDevice error:', error);
      throw error;
    }
  }

  async scanDevicePorts(ip: string): Promise<PortScanResult> {
    try {
      if (!NativeAndroidNetworkTools) {
        throw new Error('AndroidNetworkTools native module not available');
      }
      return await NativeAndroidNetworkTools.scanDevicePorts(ip);
    } catch (error) {
      console.error('AndroidNetworkTools.scanDevicePorts error:', error);
      throw error;
    }
  }

  async getEnhancedDeviceInfo(ip: string): Promise<EnhancedDeviceInfo> {
    try {
      // 组合多个功能获取增强信息
      const [pingResult, portResult] = await Promise.all([
        this.pingDevice(ip),
        this.scanDevicePorts(ip)
      ]);

      // 基于开放端口推断设备类型
      let detectedType = 'Unknown';
      const openPorts = portResult.openPorts;
      
      if (openPorts.includes(80) || openPorts.includes(443)) {
        detectedType = 'Web Server';
      } else if (openPorts.includes(22)) {
        detectedType = 'SSH Server';
      } else if (openPorts.includes(21)) {
        detectedType = 'FTP Server';
      } else if (openPorts.includes(161)) {
        detectedType = 'SNMP Device';
      }

      return {
        ip,
        hostname: 'Unknown', // 简化版本
        mac: 'Unknown', // 简化版本
        detectedType,
        snmpSupported: openPorts.includes(161),
        openPorts,
        services: portResult.services.map(s => s.service)
      };
    } catch (error) {
      console.error('AndroidNetworkTools.getEnhancedDeviceInfo error:', error);
      throw error;
    }
  }

  async performFullNetworkScan(): Promise<NetworkScanResult> {
    try {
      const startTime = Date.now();
      const devices = await this.scanSubnetDevices();
      
      // 检查SNMP支持
      let snmpDevices = 0;
      for (const device of devices) {
        try {
          const portResult = await this.scanDevicePorts(device.ip);
          if (portResult.openPorts.includes(161)) {
            snmpDevices++;
          }
        } catch (error) {
          // 忽略单个设备的错误
        }
      }

      const scanDuration = Date.now() - startTime;

      return {
        devices,
        scanSummary: {
          totalDevices: devices.length,
          snmpDevices,
          scanDuration
        }
      };
    } catch (error) {
      console.error('AndroidNetworkTools.performFullNetworkScan error:', error);
      throw error;
    }
  }
}

export default new AndroidNetworkToolsWrapper();
