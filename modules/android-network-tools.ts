import { NativeModules, Platform } from 'react-native';

const { AndroidNetworkTools: NativeAndroidNetworkTools } = NativeModules;

export interface NetworkDevice {
  ip: string;
  hostname?: string;
  mac?: string;
  vendor?: string;
  isReachable: boolean;
  responseTime?: number;
}

export interface ScanProgress {
  currentIP: string;
  progress: number;
  devicesFound: NetworkDevice[];
}

export interface PingResult {
  ip: string;
  isReachable: boolean;
  responseTime?: number;
  error?: string;
}

class AndroidNetworkToolsModule {
  /**
   * 检查模块是否可用
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (Platform.OS !== 'android') {
        return false;
      }
      
      if (!NativeAndroidNetworkTools) {
        console.warn('AndroidNetworkTools native module not found');
        return false;
      }
      
      // 调用原生方法检查可用性
      const result = await NativeAndroidNetworkTools.isAvailable();
      return result === true;
    } catch (error) {
      console.error('AndroidNetworkTools.isAvailable error:', error);
      return false;
    }
  }

  /**
   * 扫描子网中的设备
   */
  async scanSubnet(
    subnet: string,
    onProgress?: (progress: ScanProgress) => void
  ): Promise<NetworkDevice[]> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('AndroidNetworkTools not available');
      }

      console.log('🔍 开始扫描子网:', subnet);
      
      const devices = await NativeAndroidNetworkTools.scanSubnet(subnet);
      console.log('📱 扫描完成，发现设备:', devices);
      
      return devices || [];
    } catch (error) {
      console.error('AndroidNetworkTools.scanSubnet error:', error);
      throw error;
    }
  }

  /**
   * Ping指定IP地址
   */
  async ping(ip: string, timeout: number = 3000): Promise<PingResult> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('AndroidNetworkTools not available');
      }

      const result = await NativeAndroidNetworkTools.ping(ip, timeout);
      return result;
    } catch (error) {
      console.error('AndroidNetworkTools.ping error:', error);
      return {
        ip,
        isReachable: false,
        error: error.message
      };
    }
  }

  /**
   * Wake-on-LAN
   */
  async wakeOnLan(mac: string, ip?: string): Promise<boolean> {
    try {
      if (!await this.isAvailable()) {
        throw new Error('AndroidNetworkTools not available');
      }

      const result = await NativeAndroidNetworkTools.wakeOnLan(mac, ip);
      return result === true;
    } catch (error) {
      console.error('AndroidNetworkTools.wakeOnLan error:', error);
      return false;
    }
  }

  /**
   * 获取本机IP地址
   */
  async getLocalIPAddress(): Promise<string | null> {
    try {
      if (!await this.isAvailable()) {
        return null;
      }

      const ip = await NativeAndroidNetworkTools.getLocalIPAddress();
      return ip || null;
    } catch (error) {
      console.error('AndroidNetworkTools.getLocalIPAddress error:', error);
      return null;
    }
  }

  /**
   * 扫描端口
   */
  async scanPort(ip: string, port: number, timeout: number = 3000): Promise<boolean> {
    try {
      if (!await this.isAvailable()) {
        return false;
      }

      const result = await NativeAndroidNetworkTools.scanPort(ip, port, timeout);
      return result === true;
    } catch (error) {
      console.error('AndroidNetworkTools.scanPort error:', error);
      return false;
    }
  }
}

// 导出单例实例
const AndroidNetworkTools = new AndroidNetworkToolsModule();
export default AndroidNetworkTools;
