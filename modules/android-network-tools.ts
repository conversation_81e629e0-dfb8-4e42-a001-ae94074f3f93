import { NativeModules } from 'react-native';

const { AndroidNetworkTools: NativeAndroidNetworkTools } = NativeModules;

export interface DeviceInfo {
  ip: string;
  hostname: string;
  mac: string;
}

export interface PingResult {
  isReachable: boolean;
  responseTime: number;
  ip: string;
}

export interface PortScanResult {
  openPorts: number[];
  services: Array<{
    port: number;
    service: string;
  }>;
  ip: string;
}

export interface EnhancedDeviceInfo {
  ip: string;
  hostname: string;
  mac: string;
  detectedType: string;
  snmpSupported: boolean;
  openPorts: number[];
  services: string[];
}

export interface NetworkScanResult {
  devices: DeviceInfo[];
  scanSummary: {
    totalDevices: number;
    snmpDevices: number;
    scanDuration: number;
  };
}

class AndroidNetworkToolsWrapper {
  async isAvailable(): Promise<boolean> {
    try {
      if (!NativeAndroidNetworkTools) {
        console.log('📱 AndroidNetworkTools 原生模块不可用，返回 false');
        return false;
      }
      const result = await NativeAndroidNetworkTools.isAvailable();
      console.log('📱 AndroidNetworkTools.isAvailable() 原生调用结果:', result);
      return result;
    } catch (error) {
      console.error('❌ AndroidNetworkTools.isAvailable error:', error);
      return false;
    }
  }

  async scanSubnetDevices(): Promise<DeviceInfo[]> {
    try {
      if (!NativeAndroidNetworkTools) {
        console.log('📱 AndroidNetworkTools 原生模块不可用，返回模拟数据');
        return this.getMockDevices();
      }

      console.log('📱 调用原生 scanSubnetDevices...');
      console.log('🔍 开始扫描子网设备...');

      const result = await NativeAndroidNetworkTools.scanSubnetDevices();
      console.log('📱 原生扫描结果:', result);
      console.log(`✅ 子网扫描完成，发现 ${result.length} 个设备`);

      // 打印每个发现的设备
      result.forEach((device: DeviceInfo, index: number) => {
        console.log(`📱 设备 ${index + 1}: ${device.ip} (${device.hostname})`);
      });

      return result;
    } catch (error) {
      console.error('❌ AndroidNetworkTools.scanSubnetDevices error:', error);
      console.log('📱 回退到模拟数据');
      return this.getMockDevices();
    }
  }

  async pingDevice(ip: string): Promise<PingResult> {
    try {
      if (!NativeAndroidNetworkTools) {
        console.log(`📱 AndroidNetworkTools 不可用，模拟 ping ${ip}`);
        return {
          isReachable: Math.random() > 0.3, // 70% 概率可达
          responseTime: Math.floor(Math.random() * 100) + 10,
          ip
        };
      }
      
      console.log(`📱 调用原生 pingDevice: ${ip}`);
      const result = await NativeAndroidNetworkTools.pingDevice(ip);
      console.log(`📱 ping 结果:`, result);
      return result;
    } catch (error) {
      console.error(`❌ AndroidNetworkTools.pingDevice error for ${ip}:`, error);
      return {
        isReachable: false,
        responseTime: 0,
        ip
      };
    }
  }

  async scanDevicePorts(ip: string): Promise<PortScanResult> {
    try {
      if (!NativeAndroidNetworkTools) {
        console.log(`📱 AndroidNetworkTools 不可用，模拟端口扫描 ${ip}`);
        return this.getMockPortScan(ip);
      }
      
      console.log(`📱 调用原生 scanDevicePorts: ${ip}`);
      const result = await NativeAndroidNetworkTools.scanDevicePorts(ip);
      console.log(`📱 端口扫描结果:`, result);
      return result;
    } catch (error) {
      console.error(`❌ AndroidNetworkTools.scanDevicePorts error for ${ip}:`, error);
      return this.getMockPortScan(ip);
    }
  }

  async getEnhancedDeviceInfo(ip: string): Promise<EnhancedDeviceInfo> {
    try {
      // 组合多个功能获取增强信息
      const [pingResult, portResult] = await Promise.all([
        this.pingDevice(ip),
        this.scanDevicePorts(ip)
      ]);

      // 基于开放端口推断设备类型
      let detectedType = 'unknown';
      const openPorts = portResult.openPorts;
      
      if (openPorts.includes(80) || openPorts.includes(443)) {
        detectedType = 'router';
      } else if (openPorts.includes(22)) {
        detectedType = 'server';
      } else if (openPorts.includes(21)) {
        detectedType = 'server';
      } else if (openPorts.includes(161)) {
        detectedType = 'router';
      } else if (openPorts.includes(9100)) {
        detectedType = 'printer';
      }

      return {
        ip,
        hostname: `device-${ip.split('.').pop()}`,
        mac: this.generateMockMac(),
        detectedType,
        snmpSupported: openPorts.includes(161),
        openPorts,
        services: portResult.services.map(s => s.service)
      };
    } catch (error) {
      console.error('❌ AndroidNetworkTools.getEnhancedDeviceInfo error:', error);
      throw error;
    }
  }

  async performFullNetworkScan(): Promise<NetworkScanResult> {
    try {
      console.log('🚀 开始完整网络扫描...');
      const startTime = Date.now();
      
      const devices = await this.scanSubnetDevices();
      console.log(`📊 基础扫描完成，发现 ${devices.length} 个设备`);
      
      // 检查SNMP支持
      let snmpDevices = 0;
      for (const device of devices) {
        try {
          const portResult = await this.scanDevicePorts(device.ip);
          if (portResult.openPorts.includes(161)) {
            snmpDevices++;
          }
        } catch (error) {
          // 忽略单个设备的错误
          console.warn(`⚠️ 检查设备 ${device.ip} SNMP 支持时出错:`, error);
        }
      }

      const scanDuration = Date.now() - startTime;
      console.log(`✅ 完整扫描完成，耗时 ${scanDuration}ms`);

      return {
        devices,
        scanSummary: {
          totalDevices: devices.length,
          snmpDevices,
          scanDuration
        }
      };
    } catch (error) {
      console.error('❌ AndroidNetworkTools.performFullNetworkScan error:', error);
      throw error;
    }
  }

  // 模拟数据生成方法
  private getMockDevices(): DeviceInfo[] {
    console.log('📱 生成模拟设备数据...');
    return [
      {
        ip: '***********',
        hostname: 'router-gateway',
        mac: '00:11:22:33:44:55'
      },
      {
        ip: '***********00',
        hostname: 'hp-printer-01',
        mac: '00:11:22:33:44:66'
      },
      {
        ip: '*************',
        hostname: 'xiaomi-camera',
        mac: '00:11:22:33:44:77'
      },
      {
        ip: '*************',
        hostname: 'smart-tv',
        mac: '00:11:22:33:44:88'
      }
    ];
  }

  private getMockPortScan(ip: string): PortScanResult {
    const commonPorts = [22, 80, 443, 161, 9100];
    const openPorts = commonPorts.filter(() => Math.random() > 0.7); // 30% 概率开放
    
    return {
      openPorts,
      services: openPorts.map(port => ({
        port,
        service: this.getServiceName(port)
      })),
      ip
    };
  }

  private getServiceName(port: number): string {
    const serviceMap: { [key: number]: string } = {
      22: 'SSH',
      80: 'HTTP',
      443: 'HTTPS',
      161: 'SNMP',
      9100: 'Printer'
    };
    return serviceMap[port] || 'Unknown';
  }

  private generateMockMac(): string {
    const chars = '0123456789ABCDEF';
    let mac = '';
    for (let i = 0; i < 6; i++) {
      if (i > 0) mac += ':';
      mac += chars[Math.floor(Math.random() * 16)];
      mac += chars[Math.floor(Math.random() * 16)];
    }
    return mac;
  }
}

export default new AndroidNetworkToolsWrapper();
