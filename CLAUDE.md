# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run dev` - Start Expo development server (with telemetry disabled)
- `npm run android` - Run on Android device/emulator
- `npm run ios` - Run on iOS device/simulator
- `npm run build:web` - Build for web platform
- `npm run lint` - Run Expo linter

### Project Structure
This is a React Native app built with Expo and Expo Router for navigation. The app is a Smart Device Management system with network scanning capabilities.

## Architecture Overview

### Navigation Structure
- Uses Expo Router with file-based routing
- Main navigation: `app/(tabs)/` contains tab-based screens
- Screens: index (home), scanner, monitor, diagnosis, settings
- Root layout wraps everything with `DeviceScanProvider`

### Core Context & State Management
- `DeviceScanContext` - Global state for device scanning and network discovery
- Provides device list, scan progress, and scanning operations
- Uses React Context pattern for state management

### Native Module Integration
- **AndroidNetworkToolsModule** - Custom native Android module for network operations
- Located in: `android/app/src/main/java/com/diegoinsh/boltexponativewind/AndroidNetworkToolsModule.java`
- TypeScript interface: `modules/android-network-tools.ts`
- Provides: WiFi info, network scanning, ping, port scanning, Wake-on-LAN

### Key Services
- **NetworkInfoService** - Wrapper around native network tools
- **SNMPService** - SNMP protocol implementation for device monitoring
- Both services use the AndroidNetworkTools native module

### Device Scanning Architecture
- Uses multi-threaded subnet scanning (50 concurrent threads)
- Scans IP ranges (1-254) with 1-second timeout per device
- Automatically infers device types based on hostname patterns
- Real-time progress updates via React Context
- Supports device refresh and ping operations

### Component Structure
- **DeviceGridCard** & **DeviceListItem** - Device display components
- **ScanProgressIndicator** - Shows scanning progress
- **StatusBanner** - Network status display
- **QuickActionButton** - Action buttons with icons

### TypeScript Configuration
- Uses strict mode with path aliases (`@/*` maps to root)
- Expo-specific TypeScript base configuration
- Includes custom type definitions for native modules

## Development Notes

### Android-Specific Features
- Network scanning only works on Android (native module)
- Requires development build (not Expo Go) for native network tools
- Uses Android network permissions automatically

### Network Scanning Implementation
- Scans local subnet based on device's WiFi IP address
- Concurrent scanning with ExecutorService (50 threads)
- Hostname resolution for device identification
- Device type inference based on hostname patterns (router, printer, camera, etc.)

### State Management Pattern
- Global device scan state via React Context
- Progress tracking with phases: idle, scanning, analyzing, completed, error
- Device list with online/offline status and last seen timestamps