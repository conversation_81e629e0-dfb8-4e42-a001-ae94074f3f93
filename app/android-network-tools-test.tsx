import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert } from 'react-native';
import AndroidNetworkTools from '../modules/android-network-tools';

export default function AndroidNetworkToolsTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testModuleAvailability = async () => {
    setIsLoading(true);
    try {
      const isAvailable = await AndroidNetworkTools.isAvailable();
      addResult(`✅ 模块可用性测试: ${isAvailable ? '成功' : '失败'}`);
    } catch (error) {
      addResult(`❌ 模块可用性测试失败: ${error}`);
    }
    setIsLoading(false);
  };

  const testARPScan = async () => {
    setIsLoading(true);
    addResult('🔍 开始ARP扫描...');
    
    try {
      const devices = await AndroidNetworkTools.scanSubnetDevices();
      addResult(`✅ ARP扫描完成，发现 ${devices.length} 个设备:`);
      
      devices.forEach((device, index) => {
        addResult(`  ${index + 1}. ${device.ip} - ${device.hostname} (${device.mac})`);
      });
    } catch (error) {
      addResult(`❌ ARP扫描失败: ${error}`);
    }
    setIsLoading(false);
  };

  const testPing = async () => {
    setIsLoading(true);
    const testIP = '***********'; // 通常是路由器
    addResult(`🏓 Ping测试: ${testIP}`);
    
    try {
      const result = await AndroidNetworkTools.pingDevice(testIP);
      addResult(`✅ Ping结果: ${result.isReachable ? '可达' : '不可达'} (${result.responseTime}ms)`);
    } catch (error) {
      addResult(`❌ Ping测试失败: ${error}`);
    }
    setIsLoading(false);
  };

  const testPortScan = async () => {
    setIsLoading(true);
    const testIP = '***********';
    addResult(`🔌 端口扫描: ${testIP}`);
    
    try {
      const result = await AndroidNetworkTools.scanDevicePorts(testIP);
      addResult(`✅ 端口扫描完成，发现 ${result.openPorts.length} 个开放端口:`);
      
      result.services.forEach((service) => {
        addResult(`  端口 ${service.port}: ${service.service}`);
      });
    } catch (error) {
      addResult(`❌ 端口扫描失败: ${error}`);
    }
    setIsLoading(false);
  };

  const runAllTests = async () => {
    clearResults();
    addResult('🚀 开始完整测试...');
    
    await testModuleAvailability();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testPing();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testPortScan();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testARPScan();
    
    addResult('🎉 所有测试完成！');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>AndroidNetworkTools 集成测试</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]} 
          onPress={runAllTests}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>运行所有测试</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={testARPScan}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>ARP扫描</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton]} 
          onPress={clearResults}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>清除结果</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {isLoading && (
          <Text style={styles.loadingText}>⏳ 测试进行中...</Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  button: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    marginVertical: 5,
    minWidth: 100,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#34C759',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    maxHeight: 400,
  },
  resultText: {
    fontSize: 14,
    marginVertical: 2,
    fontFamily: 'monospace',
    color: '#333',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#007AFF',
    fontWeight: '600',
    marginTop: 10,
  },
});
