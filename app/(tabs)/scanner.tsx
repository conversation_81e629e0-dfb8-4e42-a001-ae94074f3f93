import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { Stack, useRouter, useNavigation } from 'expo-router';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { ArrowLeft, Camera as FlipCamera, Info, CircleCheck as CheckCircle, Cpu } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';

export default function ScannerScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');
  const [scanned, setScanned] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState(null);

  // 当页面获得焦点时隐藏底部导航栏，失去焦点时显示
  useFocusEffect(
    React.useCallback(() => {
      // 页面获得焦点时隐藏底部导航栏
      const parent = navigation.getParent();
      if (parent) {
        parent.setOptions({
          tabBarStyle: { display: 'none' }
        });
      }

      return () => {
        // 页面失去焦点时恢复底部导航栏
        if (parent) {
          parent.setOptions({
            tabBarStyle: {
              backgroundColor: '#FFFFFF',
              borderTopColor: '#F1F5F9',
              height: 60,
              paddingBottom: 8,
              display: 'flex'
            }
          });
        }
      };
    }, [navigation])
  );

  // Mock device data (would normally come from API)
  const mockDeviceInfo = {
    id: 'printer-001',
    name: '智能打印机',
    model: 'HP LaserJet Pro M428fdw',
    type: 'printer',
    status: 'active',
  };

  const handleBarCodeScanned = ({ type, data }) => {
    setScanned(true);
    // Simulate API call to get device info
    setTimeout(() => {
      setDeviceInfo(mockDeviceInfo);
    }, 500);
  };

  const resetScanner = () => {
    setScanned(false);
    setDeviceInfo(null);
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const handleBackPress = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push('/');
    }
  };

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.container}>
        <Text>Loading camera permissions...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions not granted
    return (
      <>
        <Stack.Screen
          options={{
            headerShown: false,
          }}
        />

        {/* Header部分 */}
        <View style={styles.headerContainer}>
          <LinearGradient
            colors={['#7B68EE', '#6C5CE7']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.header}
          >
            <View style={styles.headerContent}>
              <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
                <ArrowLeft color="#FFFFFF" size={24} />
              </TouchableOpacity>
              <View style={styles.logoContainer}>
                <View style={styles.logoIcon}>
                  <Cpu size={24} color="#FFFFFF" />
                </View>
                <View style={styles.logoText}>
                  <Text style={styles.appName}>SmartDM</Text>
                  <Text style={styles.slogan}>智能设备管理专家</Text>
                </View>
              </View>
              <View style={styles.placeholder} />
            </View>
          </LinearGradient>
          <View style={styles.headerCurve} />
        </View>

        <View style={styles.container}>
          <View style={styles.permissionContainer}>
            <Text style={styles.permissionTitle}>需要相机权限</Text>
            <Text style={styles.permissionText}>我们需要相机权限来扫描设备二维码</Text>
            <TouchableOpacity 
              style={styles.permissionButton}
              onPress={requestPermission}
            >
              <Text style={styles.permissionButtonText}>授予权限</Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <View style={styles.fullScreenContainer}>
        {!scanned ? (
          <>
            <CameraView
              style={styles.camera}
              facing={facing}
              barCodeScannerSettings={{
                barCodeTypes: ['qr', 'code128'],
              }}
              onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
            />

            {/* 顶部返回按钮 - 使用绝对定位 */}
            <TouchableOpacity onPress={handleBackPress} style={styles.backButtonOverlay}>
              <ArrowLeft color="#FFFFFF" size={24} />
            </TouchableOpacity>

            {/* 扫描覆盖层 - 分层设计，不阻挡相机交互 */}
            {/* 顶部遮罩 */}
            <View style={styles.topMask} />
            {/* 底部遮罩 */}
            <View style={styles.bottomMask} />
            {/* 左侧遮罩 */}
            <View style={styles.leftMask} />
            {/* 右侧遮罩 */}
            <View style={styles.rightMask} />

            {/* 扫描框架 - 不阻挡交互 */}
            <View style={styles.scanFrameContainer}>
              <View style={styles.scanFrame}>
                <View style={styles.cornerTopLeft} />
                <View style={styles.cornerTopRight} />
                <View style={styles.cornerBottomLeft} />
                <View style={styles.cornerBottomRight} />
              </View>
            </View>

            {/* 扫描提示文字 */}
            <View style={styles.scanTextContainer}>
              <Text style={styles.scanText}>将二维码放入框内，自动扫描</Text>
            </View>

            <TouchableOpacity
              style={styles.flipButton}
              onPress={toggleCameraFacing}
            >
              <FlipCamera color="#FFFFFF" size={24} />
            </TouchableOpacity>
          </>
        ) : (
          <>
            {/* 扫描结果页面的Header */}
            <View style={styles.headerContainer}>
              <LinearGradient
                colors={['#7B68EE', '#6C5CE7']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.header}
              >
                <View style={styles.headerContent}>
                  <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
                    <ArrowLeft color="#FFFFFF" size={24} />
                  </TouchableOpacity>
                  <View style={styles.logoContainer}>
                    <View style={styles.logoIcon}>
                      <Cpu size={24} color="#FFFFFF" />
                    </View>
                    <View style={styles.logoText}>
                      <Text style={styles.appName}>SmartDM</Text>
                      <Text style={styles.slogan}>智能设备管理专家</Text>
                    </View>
                  </View>
                  <View style={styles.placeholder} />
                </View>
              </LinearGradient>
              <View style={styles.headerCurve} />
            </View>

            <View style={styles.resultContainer}>
              {deviceInfo ? (
                <>
                  <View style={styles.successIcon}>
                    <CheckCircle color="#4CAF50" size={64} />
                  </View>
                  <Text style={styles.resultTitle}>扫描成功</Text>
                  <View style={styles.deviceInfoCard}>
                    <Text style={styles.deviceName}>{deviceInfo.name}</Text>
                    <Text style={styles.deviceModel}>{deviceInfo.model}</Text>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>设备ID:</Text>
                      <Text style={styles.infoValue}>{deviceInfo.id}</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>设备类型:</Text>
                      <Text style={styles.infoValue}>打印机</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>状态:</Text>
                      <Text style={[styles.infoValue, styles.statusActive]}>正常</Text>
                    </View>
                  </View>
                  <View style={styles.actionButtons}>
                    <TouchableOpacity 
                      style={styles.secondaryButton}
                      onPress={resetScanner}
                    >
                      <Text style={styles.secondaryButtonText}>继续扫描</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.primaryButton}
                      onPress={() => router.push(`/devices/${deviceInfo.id}`)}
                    >
                      <Text style={styles.primaryButtonText}>查看详情</Text>
                    </TouchableOpacity>
                  </View>
                </>
              ) : (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>正在识别设备信息...</Text>
                </View>
              )}
            </View>
          </>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#000000', // 相机背景色
  },
  headerContainer: {
    height: 90,
    backgroundColor: 'transparent',
  },
  header: {
    height: 80,
    paddingTop: 20,
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: -40, // 补偿返回按钮的宽度，使logo居中
  },
  logoIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoText: {
    alignItems: 'center',
  },
  appName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  slogan: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
    marginTop: 2,
  },
  placeholder: {
    width: 40, // 与返回按钮同宽，保持平衡
  },
  headerCurve: {
    height: 15, // 与其他页面保持一致
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -15, // 与其他页面保持一致
    zIndex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: '#7B68EE',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  camera: {
    flex: 1,
  },
  backButtonOverlay: {
    position: 'absolute',
    top: 50, // 状态栏下方
    left: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  // 分层遮罩，不阻挡相机交互
  topMask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%', // 扫描框上方
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 100,
  },
  bottomMask: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '30%', // 扫描框下方
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 100,
  },
  leftMask: {
    position: 'absolute',
    top: '30%',
    bottom: '30%',
    left: 0,
    width: '20%', // 扫描框左侧
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 100,
  },
  rightMask: {
    position: 'absolute',
    top: '30%',
    bottom: '30%',
    right: 0,
    width: '20%', // 扫描框右侧
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 100,
  },
  scanFrameContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 200,
    pointerEvents: 'none', // 不阻挡触摸事件
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 0,
    position: 'relative',
  },
  scanTextContainer: {
    position: 'absolute',
    bottom: '25%',
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 200,
    pointerEvents: 'none', // 不阻挡触摸事件
  },
  cornerTopLeft: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerTopRight: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: '#FFFFFF',
  },
  scanText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  flipButton: {
    position: 'absolute',
    bottom: 40,
    right: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F7F9FC',
    marginTop: 0, // 因为已经有header了，不需要额外margin
  },
  successIcon: {
    marginBottom: 24,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 24,
  },
  deviceInfoCard: {
    width: '100%',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  deviceModel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  infoLabel: {
    width: 80,
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  statusActive: {
    color: '#4CAF50',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#7B68EE',
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
});