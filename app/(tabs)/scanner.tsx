import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { ArrowLeft, Camera as FlipCamera, Info, CircleCheck as CheckCircle } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function ScannerScreen() {
  const router = useRouter();
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');
  const [scanned, setScanned] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState(null);

  // Mock device data (would normally come from API)
  const mockDeviceInfo = {
    id: 'printer-001',
    name: '智能打印机',
    model: 'HP LaserJet Pro M428fdw',
    type: 'printer',
    status: 'active',
  };

  const handleBarCodeScanned = ({ type, data }) => {
    setScanned(true);
    // Simulate API call to get device info
    setTimeout(() => {
      setDeviceInfo(mockDeviceInfo);
    }, 500);
  };

  const resetScanner = () => {
    setScanned(false);
    setDeviceInfo(null);
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const handleBackPress = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push('/');
    }
  };

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.container}>
        <Text>Loading camera permissions...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions not granted
    return (
      <>
        <Stack.Screen 
          options={{
            headerShown: true,
            title: '扫码识别',
            headerLeft: () => (
              <TouchableOpacity onPress={handleBackPress}>
                <ArrowLeft color="#FFFFFF" size={24} />
              </TouchableOpacity>
            ),
            headerBackground: () => (
              <LinearGradient
                colors={['#7B68EE', '#6C5CE7']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={{ flex: 1 }}
              />
            ),
            headerTintColor: '#FFFFFF',
          }}
        />
        <View style={styles.container}>
          <View style={styles.headerCurve} />
          <View style={styles.permissionContainer}>
            <Text style={styles.permissionTitle}>需要相机权限</Text>
            <Text style={styles.permissionText}>我们需要相机权限来扫描设备二维码</Text>
            <TouchableOpacity 
              style={styles.permissionButton}
              onPress={requestPermission}
            >
              <Text style={styles.permissionButtonText}>授予权限</Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: true,
          title: '扫码识别',
          headerLeft: () => (
            <TouchableOpacity onPress={handleBackPress}>
              <ArrowLeft color="#FFFFFF" size={24} />
            </TouchableOpacity>
          ),
          headerBackground: () => (
            <LinearGradient
              colors={['#7B68EE', '#6C5CE7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ flex: 1 }}
            />
          ),
          headerTintColor: '#FFFFFF',
        }}
      />
      <View style={styles.container}>
        {!scanned ? (
          <>
            <CameraView 
              style={styles.camera}
              facing={facing}
              barCodeScannerSettings={{
                barCodeTypes: ['qr', 'code128'],
              }}
              onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
            >
              <View style={styles.scanOverlay}>
                <View style={styles.scanFrame}>
                  <View style={styles.cornerTopLeft} />
                  <View style={styles.cornerTopRight} />
                  <View style={styles.cornerBottomLeft} />
                  <View style={styles.cornerBottomRight} />
                </View>
                <Text style={styles.scanText}>将二维码放入框内，自动扫描</Text>
              </View>
            </CameraView>
            <TouchableOpacity 
              style={styles.flipButton}
              onPress={toggleCameraFacing}
            >
              <FlipCamera color="#FFFFFF" size={24} />
            </TouchableOpacity>
          </>
        ) : (
          <>
            <View style={styles.headerCurve} />
            <View style={styles.resultContainer}>
              {deviceInfo ? (
                <>
                  <View style={styles.successIcon}>
                    <CheckCircle color="#4CAF50" size={64} />
                  </View>
                  <Text style={styles.resultTitle}>扫描成功</Text>
                  <View style={styles.deviceInfoCard}>
                    <Text style={styles.deviceName}>{deviceInfo.name}</Text>
                    <Text style={styles.deviceModel}>{deviceInfo.model}</Text>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>设备ID:</Text>
                      <Text style={styles.infoValue}>{deviceInfo.id}</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>设备类型:</Text>
                      <Text style={styles.infoValue}>打印机</Text>
                    </View>
                    <View style={styles.infoRow}>
                      <Text style={styles.infoLabel}>状态:</Text>
                      <Text style={[styles.infoValue, styles.statusActive]}>正常</Text>
                    </View>
                  </View>
                  <View style={styles.actionButtons}>
                    <TouchableOpacity 
                      style={styles.secondaryButton}
                      onPress={resetScanner}
                    >
                      <Text style={styles.secondaryButtonText}>继续扫描</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={styles.primaryButton}
                      onPress={() => router.push(`/devices/${deviceInfo.id}`)}
                    >
                      <Text style={styles.primaryButtonText}>查看详情</Text>
                    </TouchableOpacity>
                  </View>
                </>
              ) : (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>正在识别设备信息...</Text>
                </View>
              )}
            </View>
          </>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerCurve: {
    height: 20, // 减小高度，减少对导航栏文字的遮挡
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -20, // 相应调整marginTop
    zIndex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: '#7B68EE',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  camera: {
    flex: 1,
  },
  scanOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 0,
    position: 'relative',
  },
  cornerTopLeft: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerTopRight: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 30,
    height: 30,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#FFFFFF',
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: '#FFFFFF',
  },
  scanText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 32,
  },
  flipButton: {
    position: 'absolute',
    bottom: 40,
    right: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F7F9FC',
  },
  successIcon: {
    marginBottom: 24,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 24,
  },
  deviceInfoCard: {
    width: '100%',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  deviceModel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  infoLabel: {
    width: 80,
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  statusActive: {
    color: '#4CAF50',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#7B68EE',
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
});