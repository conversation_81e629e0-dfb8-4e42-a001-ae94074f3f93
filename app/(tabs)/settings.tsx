import React from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { Stack } from 'expo-router';
import { User, Bell, Shield, HardDrive, Globe, Info, ChevronRight } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function SettingsScreen() {
  const [notifications, setNotifications] = React.useState(true);
  const [darkMode, setDarkMode] = React.useState(false);
  const [autoScan, setAutoScan] = React.useState(true);
  
  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: true,
          title: '设置',
          headerBackground: () => (
            <LinearGradient
              colors={['#7B68EE', '#6C5CE7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ flex: 1 }}
            />
          ),
          headerTintColor: '#FFFFFF',
        }}
      />
      <View style={styles.container}>
        <View style={styles.headerCurve} />
        
        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>账户</Text>
            <View style={styles.sectionCard}>
              <TouchableOpacity style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <User size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>个人信息</Text>
                  <Text style={styles.settingDescription}>管理您的个人资料</Text>
                </View>
                <ChevronRight size={20} color="#94A3B8" />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.settingItem, styles.lastItem]}>
                <View style={styles.settingIconContainer}>
                  <Shield size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>安全设置</Text>
                  <Text style={styles.settingDescription}>密码与隐私保护</Text>
                </View>
                <ChevronRight size={20} color="#94A3B8" />
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>应用设置</Text>
            <View style={styles.sectionCard}>
              <View style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <Bell size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>通知提醒</Text>
                  <Text style={styles.settingDescription}>设备状态变更通知</Text>
                </View>
                <Switch
                  value={notifications}
                  onValueChange={setNotifications}
                  trackColor={{ false: '#E2E8F0', true: '#20B2AA' }}
                  thumbColor="#FFFFFF"
                />
              </View>
              <View style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <HardDrive size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>自动扫描设备</Text>
                  <Text style={styles.settingDescription}>连接WiFi时自动扫描</Text>
                </View>
                <Switch
                  value={autoScan}
                  onValueChange={setAutoScan}
                  trackColor={{ false: '#E2E8F0', true: '#20B2AA' }}
                  thumbColor="#FFFFFF"
                />
              </View>
              <View style={[styles.settingItem, styles.lastItem]}>
                <View style={styles.settingIconContainer}>
                  <Globe size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>暗黑模式</Text>
                  <Text style={styles.settingDescription}>切换界面显示模式</Text>
                </View>
                <Switch
                  value={darkMode}
                  onValueChange={setDarkMode}
                  trackColor={{ false: '#E2E8F0', true: '#20B2AA' }}
                  thumbColor="#FFFFFF"
                />
              </View>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>关于</Text>
            <View style={styles.sectionCard}>
              <TouchableOpacity style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <Info size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>关于应用</Text>
                  <Text style={styles.settingDescription}>版本 1.0.0</Text>
                </View>
                <ChevronRight size={20} color="#94A3B8" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.settingItem}>
                <View style={styles.settingIconContainer}>
                  <Shield size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>隐私政策</Text>
                </View>
                <ChevronRight size={20} color="#94A3B8" />
              </TouchableOpacity>
              <TouchableOpacity style={[styles.settingItem, styles.lastItem]}>
                <View style={styles.settingIconContainer}>
                  <Info size={20} color="#7B68EE" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>用户协议</Text>
                </View>
                <ChevronRight size={20} color="#94A3B8" />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerCurve: {
    height: 30,
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -15,
    zIndex: 1,
  },
  scrollContainer: {
    flex: 1,
    paddingTop: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastItem: {
    borderBottomWidth: 0,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(123, 104, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: '#94A3B8',
  },
});