import React from 'react';
import { ScrollView, StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Camera, Search, Wifi, CircleAlert as AlertCircle, Package, CircleHelp as HelpCircle, Settings } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import DeviceGridCard from '@/components/DeviceGridCard';
import QuickActionButton from '@/components/QuickActionButton';
import SectionHeader from '@/components/SectionHeader';
import StatusBanner from '@/components/StatusBanner';
import MaskedView from '@react-native-masked-view/masked-view';

export default function HomeScreen() {
  const router = useRouter();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.headerContainer}>
        <LinearGradient
          colors={['#7B68EE', '#6C5CE7']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <View style={styles.logoIcon}>
                <Settings size={24} color="#FFFFFF" />
              </View>
              <View style={styles.logoText}>
                <MaskedView
                  maskElement={<Text style={styles.appName}>SmartDM</Text>}
                >
                  <LinearGradient
                    colors={['#FFFFFF', '#E0E7FF']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={[styles.appName, styles.gradientText]}>SmartDM</Text>
                  </LinearGradient>
                </MaskedView>
                <MaskedView
                  maskElement={<Text style={styles.slogan}>智能设备管理专家</Text>}
                >
                  <LinearGradient
                    colors={['#FFFFFF', '#E0E7FF']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Text style={[styles.slogan, styles.gradientText]}>智能设备管理专家</Text>
                  </LinearGradient>
                </MaskedView>
              </View>
            </View>
          </View>
        </LinearGradient>
        <View style={styles.headerCurve} />
      </View>

      <StatusBanner 
        title="已连接到Wi-Fi网络"
        subtitle="点击扫描局域网内设备"
        type="success"
        icon={<Wifi size={20} color="#FFF" />}
      />
      
      <SectionHeader title="快捷功能" />
      <View style={styles.quickActions}>
        <QuickActionButton 
          icon={<Camera size={24} color="#7B68EE" />} 
          label="扫码识别"
          onPress={() => router.push('/scanner')}
        />
        <QuickActionButton 
          icon={<Search size={24} color="#20B2AA" />} 
          label="设备发现"
          onPress={() => router.push('/monitor')}
        />
        <QuickActionButton 
          icon={<AlertCircle size={24} color="#FF7F50" />} 
          label="智能诊断"
          onPress={() => router.push('/diagnosis')}
        />
        <QuickActionButton 
          icon={<Package size={24} color="#FFC107" />} 
          label="商品推荐"
          onPress={() => router.push('/products')}
        />
      </View>
      
      <SectionHeader 
        title="本地设备" 
        action={{
          label: "全部",
          onPress: () => router.push('/devices')
        }}
      />
      
      <View style={styles.deviceGrid}>
        <DeviceGridCard 
          deviceName="智能打印机"
          deviceModel="HP LaserJet Pro M428fdw"
          status="正常"
          statusColor="#4CAF50"
          onPress={() => router.push('/devices/printer-001')}
        />
        <DeviceGridCard 
          deviceName="网络路由器"
          deviceModel="华为 AX3 Pro"
          status="注意"
          statusColor="#FFC107"
          onPress={() => router.push('/devices/router-001')}
        />
        <DeviceGridCard 
          deviceName="智能摄像头"
          deviceModel="小米 智能摄像机 2K Pro"
          status="离线"
          statusColor="#F44336"
          onPress={() => router.push('/devices/camera-001')}
        />
        <DeviceGridCard 
          deviceName="智能电视"
          deviceModel="TCL 55V8-PRO"
          status="正常"
          statusColor="#4CAF50"
          onPress={() => router.push('/devices/tv-001')}
        />
      </View>
      
      <SectionHeader title="常见问题" />
      <View style={styles.helpSection}>
        <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/connect')}>
          <HelpCircle size={20} color="#7B68EE" />
          <Text style={styles.helpText}>如何连接新设备？</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/troubleshoot')}>
          <HelpCircle size={20} color="#7B68EE" />
          <Text style={styles.helpText}>设备无法连接怎么办？</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/update')}>
          <HelpCircle size={20} color="#7B68EE" />
          <Text style={styles.helpText}>如何更新设备固件？</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerContainer: {
    height: 90,
    backgroundColor: 'transparent',
  },
  header: {
    height: 80,
    paddingTop: 20,
  },
  headerContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  headerCurve: {
    height: 10,
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -10,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoText: {
    flex: 1,
  },
  appName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  slogan: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
    marginTop: 2,
  },
  gradientText: {
    opacity: 0,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  deviceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  helpSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  helpText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#333',
  },
});