import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Camera, Cpu, Wifi, CircleAlert as AlertCircle, Package, CircleHelp as HelpCircle, TestTube, Search, RefreshCw } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import DeviceGridCard from '../../components/DeviceGridCard';
import QuickActionButton from '../../components/QuickActionButton';
import SectionHeader from '../../components/SectionHeader';
import StatusBanner from '../../components/StatusBanner';
import ScanProgressIndicator from '../../components/ScanProgressIndicator';
import { getEnhancedNetworkInfo, checkAndRequestPermissions } from '../../services/NetworkInfoService';
import { useDeviceScan } from '../../contexts/DeviceScanContext';

export default function HomeScreen() {
  const router = useRouter();
  const [networkInfo, setNetworkInfo] = useState({
    isConnected: false,
    ssid: 'Unknown Network',
    ipAddress: '***********'
  });

  // 使用设备扫描Context
  const {
    isScanning,
    scanProgress,
    error: scanError,
    devices,
    lastScanTime,
    startNetworkScan
  } = useDeviceScan();

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      try {
        console.log('🔍 获取网络信息...');

        // 检查并请求权限
        const hasPermissions = await checkAndRequestPermissions();
        if (!hasPermissions) {
          console.warn('⚠️ 缺少必要权限');
          return;
        }

        // 获取增强的网络信息
        const info = await getEnhancedNetworkInfo();
        console.log('📊 网络信息:', info);

        setNetworkInfo({
          isConnected: info.isConnected,
          ssid: info.ssid || 'Unknown Network',
          ipAddress: info.ipAddress || '***********'
        });
      } catch (error) {
        console.error('💥 获取网络信息失败:', error);
        setNetworkInfo({
          isConnected: false,
          ssid: 'Unknown Network',
          ipAddress: '***********'
        });
      }
    };

    fetchNetworkInfo();
  }, []);

  // 处理网络扫描
  const handleNetworkScan = async () => {
    console.log('🔥 handleNetworkScan 被调用了！');
    console.log('📊 当前网络状态:', {
      isConnected: networkInfo.isConnected,
      ssid: networkInfo.ssid,
      ipAddress: networkInfo.ipAddress
    });
    console.log('📊 当前扫描状态:', {
      isScanning,
      scanProgress,
      devicesCount: devices.length
    });

    if (!networkInfo.isConnected) {
      console.log('⚠️ 网络未连接，无法扫描');
      return;
    }

    if (isScanning) {
      console.log('⚠️ 扫描已在进行中');
      return;
    }

    try {
      console.log('🚀 开始调用 startNetworkScan...');
      await startNetworkScan();
      console.log('✅ startNetworkScan 调用完成');
    } catch (error) {
      console.error('💥 启动网络扫描失败:', error);
    }
  };

  // 生成状态横幅的标题和副标题
  const getBannerContent = () => {
    if (!networkInfo.isConnected) {
      return {
        title: '网络未连接',
        subtitle: '请检查网络连接',
        type: 'error' as const,
        icon: <Wifi size={20} color="#FFF" />,
        onPress: undefined,
        disabled: true
      };
    }

    if (isScanning) {
      return {
        title: `正在扫描 ${networkInfo.ssid}`,
        subtitle: `${scanProgress.message} (${scanProgress.progress}%)`,
        type: 'info' as const,
        icon: <RefreshCw size={20} color="#FFF" />,
        onPress: undefined,
        disabled: true
      };
    }

    if (scanError) {
      const isModuleError = scanError.includes('开发构建版本') || scanError.includes('Expo Go');
      return {
        title: isModuleError ? '需要开发构建版本' : `连接到 ${networkInfo.ssid}`,
        subtitle: isModuleError ? '网络扫描需要Development Build，Expo Go不支持' : `扫描失败: ${scanError} - 点击重试`,
        type: 'warning' as const,
        icon: <AlertCircle size={20} color="#FFF" />,
        onPress: isModuleError ? undefined : handleNetworkScan,
        disabled: isModuleError
      };
    }

    if (devices.length > 0) {
      return {
        title: `已连接到 ${networkInfo.ssid}`,
        subtitle: `发现 ${devices.length} 个设备 - 点击重新扫描`,
        type: 'success' as const,
        icon: <Search size={20} color="#FFF" />,
        onPress: handleNetworkScan,
        disabled: false
      };
    }

    return {
      title: `已连接到 ${networkInfo.ssid}`,
      subtitle: `IP: ${networkInfo.ipAddress} - 点击扫描局域网内设备`,
      type: 'success' as const,
      icon: <Wifi size={20} color="#FFF" />,
      onPress: handleNetworkScan,
      disabled: false
    };
  };

  const bannerContent = getBannerContent();

  return (
    <>
      <Stack.Screen options={{ title: '生态', headerShown: false }} />

      {/* Header部分 */}
      <View style={styles.headerContainer}>
        <LinearGradient
          colors={['#7B68EE', '#6C5CE7']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <View style={styles.logoIcon}>
                <Cpu size={24} color="#FFFFFF" />
              </View>
              <View style={styles.logoText}>
                <Text style={styles.appName}>生态</Text>
                <Text style={styles.slogan}>智能设备管理专家</Text>
              </View>
            </View>
          </View>
        </LinearGradient>
        <View style={styles.headerCurve} />
      </View>

      <ScrollView style={styles.container}>
        <StatusBanner
          title={bannerContent.title}
          subtitle={bannerContent.subtitle}
          type={bannerContent.type}
          icon={bannerContent.icon}
          onPress={bannerContent.onPress}
          disabled={bannerContent.disabled}
        />

        {/* 扫描进度指示器 */}
        <ScanProgressIndicator
          progress={scanProgress}
          visible={isScanning || scanProgress.phase === 'completed' || scanProgress.phase === 'error'}
        />

        <SectionHeader title="快捷功能" />
        <View style={styles.quickActions}>
          <QuickActionButton
            icon={<Camera size={24} color="#7B68EE" />}
            label="扫码识别"
            onPress={() => router.push('/scanner')}
          />
          <QuickActionButton
            icon={<Search size={24} color="#20B2AA" />}
            label="设备发现"
            onPress={() => router.push('/monitor')}
          />
          <QuickActionButton
            icon={<AlertCircle size={24} color="#FF7F50" />}
            label="智能诊断"
            onPress={() => router.push('/diagnosis')}
          />
          <QuickActionButton
            icon={<Package size={24} color="#FFC107" />}
            label="商品推荐"
            onPress={() => router.push('/products')}
          />
        </View>

        <SectionHeader
          title="发现的设备"
          action={{
            label: "全部",
            onPress: () => router.push('/devices')
          }}
        />

        <View style={styles.deviceGrid}>
          {devices.length > 0 ? (
            devices.slice(0, 4).map((device) => (
              <DeviceGridCard
                key={device.id}
                deviceName={device.hostname || device.ip}
                deviceModel={device.deviceType}
                status={device.status === 'online' ? '在线' : '离线'}
                statusColor={device.status === 'online' ? '#4CAF50' : '#F44336'}
                onPress={() => router.push(`/devices/${device.id}`)}
              />
            ))
          ) : (
            <>
              <DeviceGridCard
                deviceName="智能打印机"
                deviceModel="HP LaserJet Pro M428fdw"
                status="正常"
                statusColor="#4CAF50"
                onPress={() => router.push('/devices/printer-001')}
              />
              <DeviceGridCard
                deviceName="网络路由器"
                deviceModel="华为 AX3 Pro"
                status="注意"
                statusColor="#FFC107"
                onPress={() => router.push('/devices/router-001')}
              />
              <DeviceGridCard
                deviceName="智能摄像头"
                deviceModel="小米 智能摄像机 2K Pro"
                status="离线"
                statusColor="#F44336"
                onPress={() => router.push('/devices/camera-001')}
              />
              <DeviceGridCard
                deviceName="智能电视"
                deviceModel="TCL 55V8-PRO"
                status="正常"
                statusColor="#4CAF50"
                onPress={() => router.push('/devices/tv-001')}
              />
            </>
          )}
        </View>

        <SectionHeader title="常见问题" />
        <View style={styles.helpSection}>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/connect')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>如何连接新设备？</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/troubleshoot')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>设备无法连接怎么办？</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.helpItem} onPress={() => router.push('/faq/update')}>
            <HelpCircle size={20} color="#7B68EE" />
            <Text style={styles.helpText}>如何更新设备固件？</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerContainer: {
    height: 90,
    backgroundColor: 'transparent',
  },
  header: {
    height: 80,
    paddingTop: 20,
  },
  headerContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  headerCurve: {
    height: 15, // 与其他页面保持一致
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -15, // 与其他页面保持一致
    zIndex: 1,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  logoText: {
    flex: 1,
  },
  appName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  slogan: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
    marginTop: 2,
  },
  gradientText: {
    opacity: 0,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  deviceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  helpSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  helpText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#333',
  },
});