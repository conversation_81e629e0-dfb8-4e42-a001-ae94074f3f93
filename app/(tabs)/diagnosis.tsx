import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { MessageSquare, Send, CircleAlert as AlertCircle, CircleCheck as CheckCircle, X } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

export default function DiagnosisScreen() {
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: '您好，我是基于Deepseek引擎的智能诊断助手。请问您遇到了什么设备问题？',
      sender: 'ai',
      timestamp: new Date(),
    },
  ]);

  const sendMessage = () => {
    if (!input.trim()) return;
    
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: input,
      sender: 'user',
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);
    
    // Simulate AI response after a delay
    setTimeout(() => {
      const mockResponse = getMockResponse(input);
      setMessages(prev => [...prev, mockResponse]);
      setIsTyping(false);
    }, 1500);
  };
  
  // Mock response generator - in a real app, this would call the Deepseek API
  const getMockResponse = (query: string): Message => {
    let responseText = '';
    
    if (query.toLowerCase().includes('打印机') && query.toLowerCase().includes('连接')) {
      responseText = '打印机连接问题通常由以下原因引起：\n\n1. 网络连接不稳定\n2. 打印机驱动程序过时\n3. 打印机IP地址变更\n\n建议您先检查打印机是否正常开机，然后确认网络连接状态，最后尝试重新安装最新的驱动程序。';
    } else if (query.toLowerCase().includes('路由器') && query.toLowerCase().includes('慢')) {
      responseText = '路由器网速慢可能由以下原因导致：\n\n1. 信道拥塞，尝试更换信道\n2. 距离路由器太远，信号衰减\n3. 同时连接设备过多\n4. 路由器固件需要更新\n\n建议您先尝试重启路由器，然后登录管理界面检查信道设置和固件版本。';
    } else {
      responseText = '感谢您的问题。根据我们的诊断库，这可能是由多种因素导致的问题。您能否提供更多关于设备型号、具体问题表现以及何时开始出现的信息？这将帮助我更准确地诊断问题。';
    }
    
    return {
      id: Date.now().toString(),
      text: responseText,
      sender: 'ai',
      timestamp: new Date(),
    };
  };

  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: true,
          title: '智能诊断',
          headerBackground: () => (
            <LinearGradient
              colors={['#7B68EE', '#6C5CE7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ flex: 1 }}
            />
          ),
          headerTintColor: '#FFFFFF',
        }}
      />
      <View style={styles.container}>
        <View style={styles.headerCurve} />
        
        <View style={styles.engineBanner}>
          <View style={styles.engineIcon}>
            <MessageSquare size={20} color="#FFFFFF" />
          </View>
          <View style={styles.engineInfo}>
            <Text style={styles.engineName}>Deepseek 智能诊断引擎</Text>
            <Text style={styles.engineDescription}>基于大语言模型的设备故障诊断系统</Text>
          </View>
          <TouchableOpacity style={styles.infoButton}>
            <AlertCircle size={18} color="#7B68EE" />
          </TouchableOpacity>
        </View>
        
        <ScrollView 
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map(message => (
            <View 
              key={message.id}
              style={[
                styles.messageWrapper,
                message.sender === 'user' ? styles.userMessageWrapper : styles.aiMessageWrapper
              ]}
            >
              <View 
                style={[
                  styles.messageBubble,
                  message.sender === 'user' ? styles.userMessage : styles.aiMessage
                ]}
              >
                <Text style={[
                  styles.messageText,
                  message.sender === 'user' && styles.userMessageText
                ]}>{message.text}</Text>
              </View>
              <Text style={styles.timestamp}>
                {message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
              </Text>
            </View>
          ))}
          
          {isTyping && (
            <View style={[styles.messageWrapper, styles.aiMessageWrapper]}>
              <View style={[styles.messageBubble, styles.aiMessage, styles.typingIndicator]}>
                <ActivityIndicator size="small" color="#7B68EE" />
                <Text style={styles.typingText}>正在思考...</Text>
              </View>
            </View>
          )}
        </ScrollView>
        
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            value={input}
            onChangeText={setInput}
            placeholder="输入您的设备问题..."
            placeholderTextColor="#94A3B8"
            multiline
          />
          <TouchableOpacity 
            style={[styles.sendButton, !input.trim() && styles.disabledSendButton]}
            onPress={sendMessage}
            disabled={!input.trim()}
          >
            <Send size={20} color={input.trim() ? '#FFFFFF' : '#CBD5E1'} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.suggestionContainer}>
          <Text style={styles.suggestionTitle}>常见问题</Text>
          <ScrollView 
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.suggestionScroll}
          >
            <TouchableOpacity 
              style={styles.suggestionChip}
              onPress={() => setInput('我的打印机无法连接到WiFi网络')}
            >
              <Text style={styles.suggestionText}>打印机无法连接WiFi</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.suggestionChip}
              onPress={() => setInput('路由器网速很慢，怎么解决？')}
            >
              <Text style={styles.suggestionText}>路由器网速很慢</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.suggestionChip}
              onPress={() => setInput('智能电视突然黑屏了')}
            >
              <Text style={styles.suggestionText}>智能电视黑屏</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.suggestionChip}
              onPress={() => setInput('如何更新设备固件？')}
            >
              <Text style={styles.suggestionText}>如何更新固件</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerCurve: {
    height: 15, // 进一步减小高度，减少对导航栏文字的遮挡
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -15, // 相应调整marginTop
    zIndex: 1,
  },
  engineBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  engineIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#7B68EE',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  engineInfo: {
    flex: 1,
  },
  engineName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  engineDescription: {
    fontSize: 12,
    color: '#666',
  },
  infoButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(123, 104, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageWrapper: {
    marginBottom: 16,
    maxWidth: '80%',
  },
  userMessageWrapper: {
    alignSelf: 'flex-end',
  },
  aiMessageWrapper: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  userMessage: {
    backgroundColor: '#7B68EE',
    borderBottomRightRadius: 4,
  },
  aiMessage: {
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  timestamp: {
    fontSize: 10,
    color: '#94A3B8',
    marginTop: 4,
    marginHorizontal: 4,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    fontSize: 14,
    color: '#7B68EE',
    marginLeft: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    alignItems: 'flex-end',
  },
  input: {
    flex: 1,
    backgroundColor: '#F8FAFD',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 14,
    color: '#333',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#7B68EE',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  disabledSendButton: {
    backgroundColor: '#E2E8F0',
  },
  suggestionContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
  },
  suggestionTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#94A3B8',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  suggestionScroll: {
    paddingHorizontal: 12,
  },
  suggestionChip: {
    backgroundColor: '#F8FAFD',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  suggestionText: {
    fontSize: 13,
    color: '#64748B',
  },
});