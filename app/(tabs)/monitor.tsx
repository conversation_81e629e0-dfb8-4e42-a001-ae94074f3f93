import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, StatusBar, Platform } from 'react-native';
import { Stack } from 'expo-router';
import { Wifi, RefreshCw, Search } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDeviceScan } from '../../contexts/DeviceScanContext';
import DeviceListItem from '@/components/DeviceListItem';
import DeviceStat from '@/components/DeviceStat';
import ScanProgressIndicator from '../../components/ScanProgressIndicator';

export default function MonitorScreen() {
  const {
    devices,
    isScanning,
    error,
    scanProgress,
    startNetworkScan,
    clearDevices
  } = useDeviceScan();
  const [selectedDevice, setSelectedDevice] = React.useState<any>(null);
  const insets = useSafeAreaInsets();

  // 动态计算header文字的垂直居中位置
  // 状态栏高度 + 导航栏高度 - headerCurve遮挡部分的一半
  const headerTitleMarginTop = -(20 / 2); // headerCurve高度的一半

  // 将ScannedDevice转换为DeviceListItem期望的Device格式
  const convertToDevice = (scannedDevice: any) => ({
    id: scannedDevice.id,
    name: scannedDevice.hostname || `设备-${scannedDevice.ip.split('.').pop()}`,
    model: `${scannedDevice.deviceType} 设备`,
    type: scannedDevice.deviceType,
    status: scannedDevice.status,
    ipAddress: scannedDevice.ip,
    stats: {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      disk: Math.floor(Math.random() * 100),
      temperature: Math.floor(Math.random() * 60) + 20,
    }
  });

  // 转换设备列表
  const convertedDevices = devices.map(convertToDevice);

  // 处理扫描设备
  const handleScanDevices = async () => {
    console.log('🔥 Monitor页面 - 扫描设备按钮被点击！');
    console.log('📊 当前扫描状态:', { isScanning, devicesCount: devices.length });

    if (isScanning) {
      console.log('⚠️ 扫描已在进行中，跳过');
      return;
    }

    try {
      console.log('🚀 开始调用 startNetworkScan...');
      await startNetworkScan();
      console.log('✅ startNetworkScan 调用完成');
    } catch (error) {
      console.error('💥 启动网络扫描失败:', error);
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: true,
          title: '设备发现',
          headerBackground: () => (
            <LinearGradient
              colors={['#7B68EE', '#6C5CE7']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ flex: 1 }}
            />
          ),
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
          },
        }}
      />
      <View style={styles.container}>
        <View style={styles.headerCurve} />
        
        <View style={styles.networkSection}>
          <View style={styles.networkInfo}>
            <Wifi size={20} color="#20B2AA" />
            <Text style={styles.networkName}>连接到: Home_WiFi_5G</Text>
          </View>
          <TouchableOpacity
            style={[styles.scanButton, isScanning && styles.scanningButton]}
            onPress={handleScanDevices}
            disabled={isScanning}
          >
            <RefreshCw size={16} color="#FFFFFF" />
            <Text style={styles.scanButtonText}>
              {isScanning ? '扫描中...' : '扫描设备'}
            </Text>
          </TouchableOpacity>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* 扫描进度指示器 */}
        <ScanProgressIndicator
          progress={scanProgress}
          visible={isScanning || scanProgress.phase === 'completed'}
        />
        
        <View style={styles.deviceListContainer}>
          <Text style={styles.sectionTitle}>设备列表</Text>
          <ScrollView 
            horizontal={true} 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.deviceList}
          >
            {convertedDevices.map(device => (
              <DeviceListItem
                key={device.id}
                device={device}
                isSelected={selectedDevice?.id === device.id}
                onPress={() => setSelectedDevice(device)}
              />
            ))}
          </ScrollView>
        </View>
        
        {selectedDevice && (
          <ScrollView style={styles.detailsContainer}>
            <View style={styles.detailsHeader}>
              <Text style={styles.deviceName}>{selectedDevice.name}</Text>
              <Text style={styles.deviceModel}>{selectedDevice.model}</Text>
              <View style={styles.deviceMeta}>
                <Text style={styles.deviceIp}>{selectedDevice.ipAddress}</Text>
                <Text style={[
                  styles.deviceStatus,
                  selectedDevice.status === 'online' && styles.statusOnline,
                  selectedDevice.status === 'unknown' && styles.statusWarning,
                  selectedDevice.status === 'offline' && styles.statusOffline,
                ]}>
                  {selectedDevice.status === 'online' && '正常'}
                  {selectedDevice.status === 'unknown' && '未知'}
                  {selectedDevice.status === 'offline' && '离线'}
                </Text>
              </View>
            </View>
            
            <Text style={styles.sectionTitle}>设备状态</Text>
            
            {selectedDevice.status !== 'offline' ? (
              <View style={styles.statsGrid}>
                <DeviceStat
                  label="CPU利用率"
                  value={`${selectedDevice.stats?.cpu || 0}%`}
                  icon="cpu"
                  progress={selectedDevice.stats?.cpu ? selectedDevice.stats.cpu / 100 : 0}
                  status={selectedDevice.stats?.cpu && selectedDevice.stats.cpu > 80 ? 'danger' :
                         selectedDevice.stats?.cpu && selectedDevice.stats.cpu > 60 ? 'warning' : 'normal'}
                />
                <DeviceStat
                  label="内存使用"
                  value={`${selectedDevice.stats?.memory || 0}%`}
                  icon="memory"
                  progress={selectedDevice.stats?.memory ? selectedDevice.stats.memory / 100 : 0}
                  status={selectedDevice.stats?.memory && selectedDevice.stats.memory > 80 ? 'danger' :
                         selectedDevice.stats?.memory && selectedDevice.stats.memory > 60 ? 'warning' : 'normal'}
                />
                <DeviceStat
                  label="存储空间"
                  value={`${selectedDevice.stats?.disk || 0}%`}
                  icon="harddrive"
                  progress={selectedDevice.stats?.disk ? selectedDevice.stats.disk / 100 : 0}
                  status={selectedDevice.stats?.disk && selectedDevice.stats.disk > 80 ? 'danger' :
                         selectedDevice.stats?.disk && selectedDevice.stats.disk > 60 ? 'warning' : 'normal'}
                />
                <DeviceStat
                  label="设备温度"
                  value={`${selectedDevice.stats?.temperature || 0}°C`}
                  icon="thermometer"
                  progress={selectedDevice.stats?.temperature ? selectedDevice.stats.temperature / 100 : 0}
                  status={selectedDevice.stats?.temperature && selectedDevice.stats.temperature > 55 ? 'danger' :
                         selectedDevice.stats?.temperature && selectedDevice.stats.temperature > 45 ? 'warning' : 'normal'}
                />
              </View>
            ) : (
              <View style={styles.offlineMessage}>
                <Text style={styles.offlineText}>设备当前离线，无法获取状态信息</Text>
                <TouchableOpacity style={styles.refreshButton}>
                  <Text style={styles.refreshButtonText}>刷新状态</Text>
                </TouchableOpacity>
              </View>
            )}
            
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>查看详情</Text>
              </TouchableOpacity>
              {selectedDevice.status !== 'offline' && (
                <TouchableOpacity style={[styles.actionButton, styles.diagnosticButton]}>
                  <Text style={styles.diagnosticButtonText}>运行诊断</Text>
                </TouchableOpacity>
              )}
            </View>
          </ScrollView>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F9FC',
  },
  headerCurve: {
    height: 15, // 进一步减小高度，减少对导航栏文字的遮挡
    backgroundColor: '#F7F9FC',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -15, // 相应调整marginTop
    zIndex: 1,
  },
  networkSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  networkInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  networkName: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#20B2AA',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  scanningButton: {
    backgroundColor: '#94A3B8',
  },
  scanButtonText: {
    color: '#FFFFFF',
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '500',
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 16,
    marginBottom: 12,
    marginTop: 16,
  },
  deviceListContainer: {
    marginBottom: 16,
  },
  deviceList: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  detailsContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 5,
    padding: 16,
  },
  detailsHeader: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    marginBottom: 16,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  deviceModel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  deviceMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deviceIp: {
    fontSize: 14,
    color: '#666',
  },
  deviceStatus: {
    fontSize: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    overflow: 'hidden',
    fontWeight: '500',
  },
  statusOnline: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    color: '#4CAF50',
  },
  statusWarning: {
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    color: '#FFC107',
  },
  statusOffline: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    color: '#F44336',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  offlineMessage: {
    backgroundColor: 'rgba(244, 67, 54, 0.05)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  offlineText: {
    color: '#F44336',
    fontSize: 14,
    marginBottom: 12,
    textAlign: 'center',
  },
  refreshButton: {
    backgroundColor: '#F44336',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  refreshButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  actionButtonText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  diagnosticButton: {
    backgroundColor: '#7B68EE',
  },
  diagnosticButtonText: {
    color: '#FFFFFF',
  },
});