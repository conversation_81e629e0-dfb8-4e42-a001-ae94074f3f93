import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { DeviceScanProvider } from '@/contexts/DeviceScanContext';

export default function RootLayout() {
  useFrameworkReady();

  return (
    <DeviceScanProvider>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </DeviceScanProvider>
  );
}
